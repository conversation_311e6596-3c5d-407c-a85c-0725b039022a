<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\Log\Log;


class QrListTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);
        $this->belongsTo('Bls', [
            'foreignKey' => 'bl_id',
            'className' => 'BL', // optional if conventional
        ]);

        // $this->belongsTo('Products', [
        //     'foreignKey' => 'product_id',
        // ]);

        // $this->belongsTo('ProductVariants', [
        //     'foreignKey' => 'varriant_id',
        // ]);
    }

    //Get qr code list by bl id
    public function getByBlId($blId)
    {
        return $this->find()
            ->where(['bl_id' => $blId])
            ->all();
    }    
    
    
}
