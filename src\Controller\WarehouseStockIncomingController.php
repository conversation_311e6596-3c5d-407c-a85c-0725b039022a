<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Database\Expression\IdentifierExpression;
use Cake\Core\Configure;
use Cake\ORM\TableRegistry;
use Cake\View\View;
use App\View\Helper\SupplierHelper;
use Cake\Log\Log;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class WarehouseStockIncomingController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $Suppliers;
    protected $Warehouses;
    protected $Products;
    protected $ProductVariants;
    protected $ProductAttributes;
    protected $StockRequests;
    protected $StockRequestItems;
    protected $SupplierPurchaseOrders;
    protected $SupplierPurchaseOrderItems;
    protected $Roles;
    protected $SupplierProducts;
    protected $StockMovements;
    protected $StockMovementItems;
    protected $ProductStocks;
    protected $BL;
    protected $BLItems;
    protected $QrList;

    protected $ProductItems;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');


        $this->Warehouses = $this->fetchTable('Warehouses');
        $this->Suppliers = $this->fetchTable('Suppliers');
        $this->Products = $this->fetchTable('Products');
        $this->ProductVariants = $this->fetchTable('ProductVariants');
        $this->ProductAttributes = $this->fetchTable('ProductAttributes');
        $this->StockRequests = $this->fetchTable('StockRequests');
        $this->StockRequestItems = $this->fetchTable('StockRequestItems');
        $this->StockMovements = $this->fetchTable('StockMovements');
        $this->StockMovementItems = $this->fetchTable('StockMovementItems');
        $this->Roles = $this->fetchTable('Roles');
        $this->SupplierProducts = $this->fetchTable('SupplierProducts');
        $this->ProductStocks = $this->fetchTable('ProductStocks');
        $this->BL = $this->fetchTable('BL');
        $this->BLItems = $this->fetchTable('BLItem');
        $this->ProductItems = $this->fetchTable('ProductItems');
        $this->QrList = $this->fetchTable('QrList');



    }
    
    public function index()
    {
        // Get the currently authenticated user
        $requested_user = $this->Authentication->getIdentity();

        if (!empty($requested_user)) {
            // Get the role of the user
            $role = $this->Roles->get($requested_user->role_id);

            // Check if the user's role is "Warehouse Manager"
            if (strtolower($role->name) === 'warehouse manager' || strtolower($role->name) === 'warehouse assistant') {
                
                if(strtolower($role->name) === 'warehouse manager')
                {
                    // Get the warehouse_id for the manager
                    $warehouse = $this->Warehouses->find()
                        ->select(['id'])
                        ->where(['manager_id' => $requested_user->id])
                        ->first();
                }
                else if(strtolower($role->name) === 'warehouse assistant')
                {
                    // Get the warehouse_id for the manager
                    $warehouse = $this->Warehouses->find()
                        ->select(['id'])
                        ->where(['assistant_id' => $requested_user->id])
                        ->first();
                }

                if (!empty($warehouse)) {
                    $warehouseId = $warehouse->id;

                    // Update the StockMovements query to filter by warehouse_id
                    $stock_movements = $this->StockMovements->find()
                        ->select([
                            'StockMovements.id',
                            'StockMovements.movement_type',
                            'StockMovements.movement_date',
                            'StockMovements.verify_status',
                            'StockMovements.created',
                            'Warehouses.name',
                            'Suppliers.name', // Fetch supplier name
                            'SupplierPurchaseOrders.id',
                            'SupplierPurchaseOrders.order_date',
                            'SupplierPurchaseOrders.bill_no',
                            'SupplierPurchaseOrders.supplier_bill_no',
                            'SupplierPurchaseOrders.payment_status'
                        ])
                        ->contain(['Warehouses']) // Keep the existing containment for warehouses
                        ->leftJoin(
                            ['StockRequests' => 'stock_requests'],
                            'StockMovements.referenceID = StockRequests.id'
                        ) // Join StockRequests table to fetch supplier_id
                        ->leftJoin(
                            ['Suppliers' => 'suppliers'],
                            'Suppliers.id = StockRequests.supplier_id'
                        ) // Join Suppliers table to fetch supplier name
                        ->leftJoin(
                            ['SupplierPurchaseOrders' => 'supplier_purchase_orders'],
                            'SupplierPurchaseOrders.stock_request_id = StockRequests.id'
                        ) // Join SupplierPurchaseOrders table to fetch purchase orders
                        ->where([
                            'StockMovements.movement_type' => 'Incoming',
                            'StockMovements.warehouse_id' => $warehouseId // Filter by warehouse_id
                        ])
                        ->order(['StockMovements.id' => 'DESC'])
                        ->toArray();
                } else {
                    $stock_movements = []; // No warehouse assigned to this manager
                }
            } else {
                // Handle cases for non-Warehouse Manager roles
                $stock_movements = $this->StockMovements->find()
                    ->select([
                        'StockMovements.id',
                        'StockMovements.movement_type',
                        'StockMovements.movement_date',
                        'StockMovements.verify_status',
                        'StockMovements.created',
                        'Warehouses.name',
                        'Suppliers.name', // Fetch supplier name
                        'SupplierPurchaseOrders.id',
                        'SupplierPurchaseOrders.order_date',
                        'SupplierPurchaseOrders.bill_no',
                        'SupplierPurchaseOrders.supplier_bill_no',
                        'SupplierPurchaseOrders.payment_status'
                    ])
                    ->contain(['Warehouses']) // Keep the existing containment for warehouses
                    ->leftJoin(
                        ['StockRequests' => 'stock_requests'],
                        'StockMovements.referenceID = StockRequests.id'
                    ) // Join StockRequests table to fetch supplier_id
                    ->leftJoin(
                        ['Suppliers' => 'suppliers'],
                        'Suppliers.id = StockRequests.supplier_id'
                    ) // Join Suppliers table to fetch supplier name
                    ->leftJoin(
                        ['SupplierPurchaseOrders' => 'supplier_purchase_orders'],
                        'SupplierPurchaseOrders.stock_request_id = StockRequests.id'
                    ) // Join SupplierPurchaseOrders table to fetch purchase orders
                    ->where([
                        'StockMovements.movement_type' => 'Incoming',
                        'StockMovements.warehouse_id IS NOT' => null
                    ])
                    ->order(['StockMovements.id' => 'DESC'])
                    ->toArray();
            }
        } else {
            $stock_movements = []; // Handle unauthenticated users
        }


        $this->set(compact('stock_movements'));
    }

    public function add()
    {

        if ($this->request->is('post')) {

            $this->loadComponent(name: 'Stock'); // Load the Stock component

            $data = $this->request->getData();

            $requested_user = $this->Authentication->getIdentity();

            if (!empty($requested_user)) {
                $role = $this->Roles->get($requested_user->role_id);
            }

            $allowedFormats = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_TYPE');
            $maxWebImageSize = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_SIZE') * 1024 * 1024;

            if (!empty($data['document']) && $data['document']->getError() === UPLOAD_ERR_OK) {
                $web_image = $data['document'];
                $webImageName = trim($web_image->getClientFilename());
                $webImageSize = $web_image->getSize();
                $webImageExt = strtolower(pathinfo($webImageName, PATHINFO_EXTENSION));

                if (!in_array($webImageExt, $allowedFormats)) {
                    return $this->Flash->error(__('Invalid file type for web image. Only ' . $allowedFormats . ' are allowed.'));
                }

                if ($webImageSize > $maxWebImageSize) {
                    return $this->Flash->error(__('Web image size exceeds the maximum allowed size of ' . $maxWebImageSize . ' MB.'));
                }

                list($width, $height) = getimagesize($web_image->getStream()->getMetadata('uri'));
                $minWidth = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MIN_WIDTH');
                $maxWidth = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MAX_WIDTH');
                $minHeight = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MIN_HEIGHT');
                $maxHeight = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MAX_HEIGHT');

                if (!empty($webImageName)) {
                    $webImageTmpName = $web_image->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Constants.INCOMING_STOCK_DOCUMENT');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $webImageFile = pathinfo($webImageName, PATHINFO_FILENAME) . '_' . $rand . '.' . $webImageExt;
                    $uploadResult = $this->Media->upload($webImageTmpName, $targetdir, $webImageFile, $uploadFolder);
                    if ($uploadResult === 'Success') {
                        $data['document'] = $uploadFolder . $webImageFile;
                    }
                }
            }
            else
            {
                $data['document'] = "";
            }

            $stockRequestId = $data['stock_request_id'];
            $movementDate = $data['movement_date'];
            $document = $data['document'];

            // // Fetch stock request details
            $stockRequest = $this->StockRequests->find()
                ->where(['StockRequests.id' => $stockRequestId])
                ->contain(['StockRequestItems'])
                ->first();

            if (!$stockRequest) {
                $this->Flash->error(__('The stock request not found.'));
            }

            // Prepare parameters for the Stock component
            $warehouse_id = $stockRequest->warehouse_id;
            $movement_date = $movementDate;
            $reference_type = 'stock_request';
            $referenceID = $stockRequestId;
            $image = $document;

            $product_id = $data['product_id'];
            $product_variant_id = $data['product_variant_id'];
            $product_attribute_id = $data['product_attribute_id'];
            $quantity = $data['quantity'];

            $stock_items = [];
            for($i=0; $i < sizeof($product_id); $i++)
            {
                $stock_items[] = [
                    'product_id' => $product_id[$i],
                    'product_variant_id' => $product_variant_id[$i],
                    'product_attribute_id' => $product_attribute_id[$i],
                    'quantity' => $quantity[$i]
                ];
            }

            // Call the Stock component's method
            // $result = $this->Stock->addWarehouseInStock($warehouse_id, $movement_date, $reference_type, $referenceID, $image, $stock_items, $driver_id = null);

            // Check user role before calling Stock component
            if (strtolower($role->name) === 'warehouse manager' || strtolower($role->name) === 'admin') {
                $result = $this->Stock->addWarehouseInStock($warehouse_id, $movementDate, $reference_type, $referenceID, $image, $stock_items, $driver_id = null);


            } elseif (strtolower($role->name) === 'warehouse assistant') {
                // Call a modified version that only inserts into StockMovements and StockMovementItems
                $result = $this->Stock->addWarehouseInStockWithoutUpdatingProductStock($warehouse_id, $movementDate, $reference_type, $referenceID, $image, $stock_items, $driver_id = null);

                if($result)
                {
                    $this->sendIncomingStockApprovalEmail($stockRequest);
                }
            }

            // Return the result as JSON
            if ($result) {

                // Step 1: Update request_status to 'Completed' in StockRequests table
                $stockRequest = $this->StockRequests->get($referenceID);

                if (strtolower($role->name) === 'warehouse manager' || strtolower($role->name) === 'admin') {
                    
                    if ($stockRequest) {

                        $stockRequest->request_status = 'Completed';
                        $this->StockRequests->save($stockRequest);
                    }

                    // Step 2: Update verify_status in StockMovements table
                    $stockMovementsTable = TableRegistry::getTableLocator()->get('StockMovements');
                    $stockMovement = $stockMovementsTable->find()
                        ->where(['reference_type' => 'stock_request', 'referenceID' => $referenceID])
                        ->first();

                    if ($stockMovement) {
                        $stockMovement->verify_status = 'Approved';
                        $stockMovementsTable->save($stockMovement);
                    }

                }

                // Step 3: Update delivery_status to 'Delivered' in SupplierPurchaseOrders table
                $supplierPurchaseOrders = TableRegistry::getTableLocator()->get('SupplierPurchaseOrders');

                $purchaseOrder = $supplierPurchaseOrders->find()
                    ->where(['stock_request_id' => $referenceID])
                    ->first();

                if ($purchaseOrder) {

                    if (strtolower($role->name) === 'warehouse manager' || strtolower($role->name) === 'admin') {
                        $purchaseOrder->delivery_status = 'Delivered';
                    }

                    // Update supplier_bill_no if available
                    if (!empty($data['supplier_bill_no'])) {
                        $purchaseOrder->supplier_bill_no = $data['supplier_bill_no'];
                    }

                    $supplierPurchaseOrders->save($purchaseOrder);
                }

                // Add missing products to supplier_purchase_order_items
                $supplierPurchaseOrderItems = TableRegistry::getTableLocator()->get('SupplierPurchaseOrdersItems');
                foreach ($stock_items as $item) {

                    $existsConditions = [
                        'supplier_purchase_order_id' => $purchaseOrder->id,
                        'product_id' => $item['product_id']
                    ];

                    // Add `product_variant_id` conditionally
                    if (!empty($item['product_variant_id']) && $item['product_variant_id'] !== 'null') {
                        $existsConditions['product_variant_id'] = $item['product_variant_id'];
                    }

                    // Add `product_attribute_id` conditionally
                    if (!empty($item['product_attribute_id']) && $item['product_attribute_id'] !== 'null') {
                        $existsConditions['product_attribute_id'] = $item['product_attribute_id'];
                    }

                    $exists = $supplierPurchaseOrderItems->exists($existsConditions);

                    if (!$exists) {

                        $newItemData = [
                            'supplier_purchase_order_id' => $purchaseOrder->id,
                            'product_id' => $item['product_id'],
                            'quantity' => $item['quantity'],
                            'approved_quantity' => $item['quantity']
                        ];

                        // Add `product_variant_id` conditionally
                        if (!empty($item['product_variant_id']) && $item['product_variant_id'] !== 'null') {
                            $newItemData['product_variant_id'] = $item['product_variant_id'];
                        }

                        // Add `product_attribute_id` conditionally
                        if (!empty($item['product_attribute_id']) && $item['product_attribute_id'] !== 'null') {
                            $newItemData['product_attribute_id'] = $item['product_attribute_id'];
                        }

                        // Create and save the new entity
                        $newItem = $supplierPurchaseOrderItems->newEntity($newItemData);
                        $supplierPurchaseOrderItems->save($newItem);
                    }
                }

                $this->Flash->success(__('The stocks have been saved.'));

                return $this->redirect(['action' => 'index']);
            
            } else {

                $this->Flash->error(__('Failed to process stock request. Please, try again.'));
            
            }
        }

        // Get the currently authenticated user
        $requested_user = $this->Authentication->getIdentity();

        if (!empty($requested_user)) {
            // Get the role of the user
            $role = $this->Roles->get($requested_user->role_id);

            // Check if the user's role is "Warehouse Manager"
            if (strtolower($role->name) === 'warehouse manager' || strtolower($role->name) === 'warehouse assistant') {
                
                // Get the warehouse_id for the manager
                if(strtolower($role->name) === 'warehouse manager')
                {
                    // Get the warehouse_id for the manager
                    $warehouse = $this->Warehouses->find()
                        ->select(['id'])
                        ->where(['manager_id' => $requested_user->id])
                        ->first();
                }
                else if(strtolower($role->name) === 'warehouse assistant')
                {
                    // Get the warehouse_id for the manager
                    $warehouse = $this->Warehouses->find()
                        ->select(['id'])
                        ->where(['assistant_id' => $requested_user->id])
                        ->first();
                }

                if (!empty($warehouse)) {
                    $warehouseId = $warehouse->id;

                    $suppliers = $this->Suppliers->find()
                        ->where(['Suppliers.status' => 'A'])
                        ->contain([
                            'SupplierPurchaseOrders' => function ($q) use ($warehouseId) {
                                $subquery = $this->StockMovements->find()
                                    ->select(['referenceID'])
                                    ->where(['referenceID IS NOT' => null]); // Ensure NULL values are not checked
                                
                                return $q->select([
                                        'SupplierPurchaseOrders.supplier_id', 
                                        'SupplierPurchaseOrders.bill_no', 
                                        'SupplierPurchaseOrders.supplier_bill_no', 
                                        'SupplierPurchaseOrders.stock_request_id'
                                    ])
                                    ->where([
                                        'SupplierPurchaseOrders.delivery_status' => 'Pending',
                                        'SupplierPurchaseOrders.status' => 'A',
                                        'SupplierPurchaseOrders.id_deliver_to' => $warehouseId,
                                        'SupplierPurchaseOrders.stock_request_id NOT IN' => $subquery
                                    ]);
                            }
                        ])
                        ->toArray();

                } else {
                    // Handle case where no warehouse is found for the manager
                    $suppliers = []; // Or any appropriate action
                }
            } else {

                $suppliers = $this->Suppliers->find()
                    ->where(['Suppliers.status' => 'A'])
                    ->contain([
                        'SupplierPurchaseOrders' => function ($q) {
                            $subquery = $this->StockMovements->find()
                                ->select(['referenceID'])
                                ->where(['referenceID IS NOT' => null]); // Ensure NULL values are not checked

                            return $q->select([
                                    'SupplierPurchaseOrders.supplier_id', 
                                    'SupplierPurchaseOrders.bill_no', 
                                    'SupplierPurchaseOrders.supplier_bill_no', 
                                    'SupplierPurchaseOrders.stock_request_id'
                                ])
                                ->where([
                                    'SupplierPurchaseOrders.delivery_status' => 'Pending',
                                    'SupplierPurchaseOrders.status' => 'A',
                                    'SupplierPurchaseOrders.stock_request_id NOT IN' => $subquery
                                ]);
                        }
                    ])
                    ->toArray();

            }
        }

        // $requested_user = $this->Authentication->getIdentity();

        $imageSize = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_SIZE');
        $webImageMinWidth = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MIN_WIDTH');
        $webImageMaxWidth = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MAX_WIDTH');
        $webImageMinHeight = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MIN_HEIGHT');
        $webImageMaxHeight = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MAX_HEIGHT');
        $webImageType = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_IMAGE_TYPE');
        $webImageTypeText = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_TEXT_TYPE');

        $file_acceptance_msg = __('Only '.$webImageTypeText.' files are allowed.'.$imageSize.'MB.');

        $this->set([
            'webImageSize' => Configure::read('Constants.INCOMING_STOCK_DOCUMENT_SIZE'),
            'webImageType' => array_map('trim', explode(',', $webImageType)),
            'webImageMinWidth' => Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MIN_WIDTH'),
            'webImageMaxWidth' => Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MAX_WIDTH'),
            'webImageMinHeight' => Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MIN_HEIGHT'),
            'webImageMaxHeight' => Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MAX_HEIGHT'),
        ]);

        $this->set(compact('suppliers', 'file_acceptance_msg')); 

    }

    private function sendIncomingStockApprovalEmail($stockRequest)
    {
        $toEmails = [];

        // Fetch Warehouse Manager
        $warehouse = $this->Warehouses->get($stockRequest->warehouse_id, [
                'contain' => ['Managers']
            ]);

        $warehouseName = $warehouse && $warehouse->manager ? $warehouse->manager->first_name.' '.$warehouse->manager->last_name : 'Unknown Manager';
        $warehouseManagerEmail = $warehouse && $warehouse->manager ? $warehouse->manager->email : null;

        $toEmails = array_filter([$warehouseManagerEmail]); // Remove null values

        // Ensure at least one recipient exists
        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipients found for incoming stock approval request ID: " . $stockRequest->id);
            return;
        }

        // Format request date as DD-MM-YY
        $formattedDate = $stockRequest->created ? $stockRequest->created->format('d-m-y') : 'N/A';

        // Set up email variables
        $emailData = [
            'stock_request_id' => $stockRequest->id,
            'warehouse_name' => $warehouse->name,
            'greeting' => "Dear Warehouse Manager,",
            'message' => "An incoming stock has been added by the warehouse assistant and requires your approval.",
            'request_date' => $formattedDate,
        ];

        $subject = "Approval Required for Incoming Stock Request (ID: #{$stockRequest->id})";

        // Use the global email sending function
        $this->Global->send_email(
            $toEmails, // Send to the appropriate recipients
            null, // Default FROM email from settings
            $subject,
            'incoming_stock_approval', // Email template name
            $emailData
        );
    }

    public function getProductsBySupplier($supplierId = null)
    {
        // Fetch products related to the selected supplier
        $products = $this->Products->find()
            ->distinct(['Products.id'])
            ->innerJoinWith('SupplierProducts') // Association name
            ->where([
                'SupplierProducts.status' => 'A',
                'SupplierProducts.supplier_id' => $supplierId
            ])
            ->order(['Products.name' => 'ASC'])
            ->toArray();
        $this->set([
                    'products' => $products,
                    '_serialize' => ['variants'],
                ]);

        return $this->response->withType('application/json')
                ->withStringBody(json_encode(['products' => $products]));

    }

    public function getStockRequestItems()
    {

        $this->request->allowMethod(['get']);

        $stockRequestId = $this->request->getQuery('stock_request_id');
        $blList = $this->BL->find()
            ->where(['stock_request_id' => $stockRequestId])
            ->contain(['BlItems'])
            ->toArray();
       $lastBl = end($blList);

        if(!empty($blList)) {
            // foreach ($lastBl as $bl) {
                foreach ($lastBl->bl_items as $bitem) {
                    $productarr = json_decode(json_encode($bitem));
                    $product = $this->Products->getProductById($productarr->product_id);
                        $product_name = $product->name;
                        if($productarr->product_variant_id && $productarr->product_variant_id != null)
                        {
                            $product_variant = $this->ProductVariants->getVariantById($productarr->product_variant_id);
                        }
                        else
                        {
                            $product_variant = null;
                        }
                       

                        // $product_variant = $bitem->_matchingData['ProductVariants']->id ? $bitem->_matchingData['ProductVariants']->variant_name : 'N/A';
                        $product_variant_name = $product_variant ? $product_variant->variant_name : 'N/A';
if($productarr->product_attribute_id && $productarr->product_attribute_id != null)
                        {
                            $product_attribute = $this->ProductAttributes->getAttributeById($productarr->product_attribute_id);
                           
                            $AttributeValues = TableRegistry::getTableLocator()->get('AttributeValues');
                            $product_attribute_name = $AttributeValues->find()
    ->select(['value'])
    ->where(['id' => $product_attribute->attribute_value_id, 'status' => 'A'])
    ->first()
    ->value ?? null;
                        }
                        else
                        {
                            $product_attribute = null;
                        }
                        
                        
                        // $product_attribute_name =  $product_attribute ? $product_attribute->attribute_description : 'N/A';
                                

                        if ($bitem->product_variant_id)
                        {
                            $sku = $product_variant->sku;
                        }
                        else
                        {
                            $sku = $product->sku;
                        }

                    $response[] = [
                        'id' => $bitem->id,
                        'product_id' => $bitem->product_id,
                        'product_variant_id' => $productarr->product_variant_id,
                        'product_attribute_id' => $productarr->product_attribute_id,
                        'product_name' => $product_name,
                        'product_variant' => $product_variant_name,
                        'product_attribute' => $product_attribute_name,
                        'sku' => $sku,
                        'requested_quantity' => $bitem->remaining_quantity,
                    ];
                }
            // }   
          
            return $this->response->withType('application/json')
            ->withStringBody(json_encode(['success' => true, 'data' => $response]));
        }
        else if ($stockRequestId) {
            $stockRequestItems = $this->StockRequests->StockRequestItems->find()
                ->select([
                    'StockRequestItems.id',
                    'StockRequestItems.requested_quantity',
                    'StockRequestItems.product_id',
                    'StockRequestItems.product_variant_id',
                    'StockRequestItems.product_attribute_id',
                    'StockRequestItems.stock_request_id',
                    'StockRequestItems.supervisor_approved_quantity',
                    'Products.name',
                    'Products.purchase_price', 
                    'Products.sku', 
                    'Products.supplier_id',  // Assuming Products has a supplier_id field
                    'ProductVariants.id', 
                    'ProductVariants.variant_name', 
                    'ProductVariants.purchase_price', 
                    'ProductVariants.sku', 
                    'Suppliers.name'  // Accessing supplier's name via Products
                ])
                ->leftJoinWith('Products')
                ->leftJoinWith('Products.Suppliers')  // Access Suppliers through Products
                ->leftJoinWith('ProductVariants')
                ->where(['StockRequestItems.stock_request_id' => $stockRequestId])
                ->toArray(); 

            // Prepare the data for the response
            $response = [];
            foreach ($stockRequestItems as $item) {

                $item->attributes = [];

                if ($item->product_attribute_id) {
                    // Fetch attributes related to the product
                    $attributes = $this->ProductAttributes->find()
                        ->where(['ProductAttributes.id' => $item->product_attribute_id])
                        ->contain([
                            'Attributes' => [
                                'fields' => ['Attributes.name']
                            ],
                            'AttributeValues' => [
                                'fields' => ['AttributeValues.value']
                            ]
                        ])
                        ->first();

                    if ($attributes) {
                        // Add attribute details to the item if found
                        $item->attributes = [
                            'attribute_name' => $attributes->attribute->name ?? '',
                            'attribute_value' => $attributes->attribute_value->value ?? ''
                        ];
                    }
                }

                $itemId = $item->id;
                $product_name = $item->_matchingData['Products']->name;
                $product_variant = $item->_matchingData['ProductVariants']->id ? $item->_matchingData['ProductVariants']->variant_name : 'N/A';
                $product_attribute = $item->attributes ? $item->attributes['attribute_name'] . ':' . $item->attributes['attribute_value'] : 'N/A';
                if ($item['product_variant_id'])
                {
                    $sku = $item->_matchingData['ProductVariants']->sku;
                }
                else
                {
                    $sku = $item->_matchingData['Products']->sku;
                }
                $supplier_name = $item->_matchingData['Suppliers']->name;
                $requested_quantity = $item->requested_quantity;

                $response[] = [
                    'id' => $itemId,
                    'product_id' => $item->product_id,
                    'product_variant_id' => $item->product_variant_id,
                    'product_attribute_id' => $item->product_attribute_id,
                    'product_name' => $product_name,
                    'product_variant' => $product_variant,
                    'product_attribute' => $product_attribute,
                    'sku' => $sku,
                    'requested_quantity' => $requested_quantity,
                ];
            }

            // Return the response as JSON
            return $this->response->withType('application/json')
            ->withStringBody(json_encode(['success' => true, 'data' => $response]));
        } else {
            // If no stock_request_id is passed, return an error response
            return $this->response->withType('application/json')
            ->withStringBody(json_encode(['success' => false, 'message' => 'Invalid request']));
        }
    }

    public function view($id = null)
    {
        // Step 1: Fetch the StockMovement and associated supplier_id
        $StockMovement = $this->StockMovements->find()
            ->select([
                'StockMovements.id',
                'StockMovements.movement_type',
                'StockMovements.movement_date',
                'StockMovements.image',
                'StockMovements.verify_status',
                'StockMovements.created',
                'Warehouses.name',
                'StockRequests.supplier_id',        // Fetch supplier_id
                'Suppliers.name',                   // Fetch supplier name
                'SupplierPurchaseOrders.id',        // Purchase order data
                'SupplierPurchaseOrders.order_date',
                'SupplierPurchaseOrders.bill_no',
                'SupplierPurchaseOrders.supplier_bill_no',
                'SupplierPurchaseOrders.payment_status',
                'SupplierPurchaseOrders.delivery_status',
                'SupplierPurchaseOrders.required_delivery_date',
                'SupplierPurchaseOrders.payment_due_date',
            ])
            ->contain(['Warehouses'])
            ->leftJoin(
                ['StockRequests' => 'stock_requests'],
                'StockMovements.referenceID = StockRequests.id'
            )
            ->leftJoin(
                ['Suppliers' => 'suppliers'],
                'StockRequests.supplier_id = Suppliers.id'
            )
            ->leftJoin(
                ['SupplierPurchaseOrders' => 'supplier_purchase_orders'],
                'SupplierPurchaseOrders.stock_request_id = StockRequests.id'
            )
            ->where(['StockMovements.id' => $id])
            ->first();

        $supplierId = $StockMovement['StockRequests']['supplier_id'] ?? null;

        // Step 2: Fetch StockMovementItems and supplier_price using supplier_id
        $StockMovementItems = $this->StockMovements->StockMovementItems->find()
            ->select([
                'StockMovementItems.id',
                'StockMovementItems.quantity',
                'StockMovementItems.product_id',
                'StockMovementItems.product_variant_id',
                'StockMovementItems.product_attribute_id',
                'StockMovementItems.stock_movement_id',
                'StockMovementItems.status',
                'Products.name',
                'Products.purchase_price', 
                'Products.sku', 
                'ProductVariants.id', 
                'ProductVariants.variant_name', 
                'ProductVariants.purchase_price', 
                'ProductVariants.sku',
            ])
            ->leftJoinWith('Products')  
            ->leftJoinWith('ProductVariants')
            ->where([
                'StockMovementItems.stock_movement_id' => $id
            ])
            ->toArray();

        foreach ($StockMovementItems as &$item) {

            $item->supplier_price = null;

            // Check if product_variant_id exists and fetch supplier_price accordingly
            if ($item->product_variant_id) {

                $supplierPriceQuery = $this->SupplierProducts->find()
                    ->select(['supplier_price'])
                    ->where([
                        'SupplierProducts.supplier_id' => $supplierId,
                        'SupplierProducts.product_id' => $item->product_id,
                        'SupplierProducts.product_variant_id' => $item->product_variant_id,
                    ])
                    ->first();
            } else {
                $supplierPriceQuery = $this->SupplierProducts->find()
                    ->select(['supplier_price'])
                    ->where([
                        'SupplierProducts.supplier_id' => $supplierId,
                        'SupplierProducts.product_id' => $item->product_id,
                    ])
                    ->first();
            }

            // Assign supplier_price if found
            if ($supplierPriceQuery) {
                $item->supplier_price = $supplierPriceQuery->supplier_price;
            }

            // Initialize an attributes array in each item
            $item->attributes = [];

            if ($item->product_attribute_id) {
                // Fetch attributes related to the product
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $item->product_attribute_id])
                    ->contain([
                        'Attributes' => [
                            'fields' => ['Attributes.name']
                        ],
                        'AttributeValues' => [
                            'fields' => ['AttributeValues.value']
                        ]
                    ])
                    ->first();

                if ($attributes) {
                    // Add attribute details to the item if found
                    $item->attributes = [
                        'attribute_name' => $attributes->attribute->name ?? '',
                        'attribute_value' => $attributes->attribute_value->value ?? ''
                    ];
                }
            }
        }

        if (!empty($StockMovement->image)) {
                $StockMovement->image = $this->Media->getCloudFrontURL($StockMovement->image);
        }

        $currencyConfig = Configure::read('Settings.Currency.format');
        $currencySymbol = isset($currencyConfig['currency_symbol']) ? $currencyConfig['currency_symbol'] : '';
        $decimalSeparator = isset($currencyConfig['decimal_separator']) ? $currencyConfig['decimal_separator'] : '';
        $thousandSeparator = isset($currencyConfig['thousand_separator']) ? $currencyConfig['thousand_separator'] : '';

        $requested_user = $this->Authentication->getIdentity();
        
        if (!empty($requested_user)) {
            // Get the role of the user
            $role = $this->Roles->get($requested_user->role_id);
        }

        $this->set(compact('StockMovement', 'StockMovementItems', 'currencySymbol', 'decimalSeparator', 'thousandSeparator', 'role')); 
    }

    public function getVariants($productId)
    {
        $this->request->allowMethod(['get']);

        // Fetch variants for the selected product
        $variants = $this->ProductVariants->find()
            ->select(['id', 'variant_name', 'sku']) // Fetch ID, name, and SKU of the variants
            ->where(['ProductVariants.product_id' => $productId]) // Filter by product_id
            ->andWhere(['ProductVariants.status' => 'A'])
            ->toArray();

        // Fetch product attributes and their values
        $productAttributes = $this->ProductAttributes->find()
            ->contain(['Attributes', 'AttributeValues'])
            ->where(['ProductAttributes.product_id' => $productId])
            ->andWhere(['ProductAttributes.status' => 'A'])
            ->toArray();

        // Prepare the response
        $response = [
            'variants' => [],
            'attributes' => []
        ];

        $variantData = [];
        foreach ($variants as $variant) {
            $response['variants'][$variant->id] = [
                'name' => $variant->variant_name,
                'sku' => $variant->sku,
            ];
        }

        foreach ($productAttributes as $productAttribute) {
            $response['attributes'][] = [
                'attribute_id' => $productAttribute->id,
                'attribute_name' => $productAttribute->attribute->name,
                'attribute_value' => $productAttribute->attribute_value->value,
            ];
        }

        $this->set([
                    'response' => $response,
                    '_serialize' => ['response'],
                ]);

        // Return JSON response
        return $this->response->withType('application/json')
                ->withStringBody(json_encode($response));
    }

    public function approve($id = null)
    {
        $requested_user = $this->Authentication->getIdentity();

        // if ($this->request->is(['patch', 'post', 'put'])) {

        //     try {

        //         $user_detail = $this->Authentication->getIdentity();

        //         if(!empty($user_detail))
        //         {
        //             $role = $this->Roles->find()
        //                     ->where(['id' => $user_detail->role_id])
        //                     ->first();

        //             if (strtolower($role->name) == 'warehouse manager' || strtolower($role->name) === 'admin') {

        //                 $data = $this->request->getData();

        //                 // echo "<pre>";print_r($data);die;

        //                 $verified_by = $user_detail->id;
        //                 $verify_status = 'Approved';
        //                 $verify_time = date('Y-m-d H:i:s');

        //                 $product_id = $this->request->getData('product_id');
        //                 $product_variant_id = $this->request->getData('product_variant_id');
        //                 $product_attribute_id = $this->request->getData('product_attribute_id');
        //                 $requested_quantity = $this->request->getData('quantity');
        //                 $accepted_quantity = $this->request->getData('accepted_quantity');
        //                 $statuses = $this->request->getData('status');

        //                 // Fetch and update the stock request data
        //                 $stock_movement_data = $this->StockMovements->get($id);

        //                 $stock_movement_data->verify_status = $verify_status;
        //                 $stock_movement_data->verify_time = $verify_time;
        //                 $stock_movement_data->verified_by = $verified_by;

        //                 if ($this->StockMovements->save($stock_movement_data)) {

        //                     $this->StockMovementItems->deleteAll(['stock_movement_id' => $id]);

        //                     $stockMovementItems = [];
        //                     for($i=0; $i < sizeof($product_id); $i++)
        //                     {
        //                         $status = (isset($statuses[$i]) && $statuses[$i] === 'Reject') ? 'Rejected' : 'Approved';

        //                         $stockMovementItems[] = [
        //                             'stock_movement_id' => $id,
        //                             'product_id' => $product_id[$i],
        //                             'product_variant_id' => !empty($product_variant_id[$i]) ? $product_variant_id[$i] : null,
        //                             'product_attribute_id' => !empty($product_attribute_id[$i]) ? $product_attribute_id[$i] : null,
        //                             'quantity' => $accepted_quantity[$i],
        //                             'status' => $status
        //                         ];
        //                     }

        //                     // Bulk insert for better performance
        //                         $entities = $this->StockMovementItems->newEntities($stockMovementItems);
        //                         if ($this->StockMovementItems->saveMany($entities)) {

        //                             $referenceID = $stock_movement_data->referenceID;

        //                             // Step 1: Update request_status to 'Completed' in StockRequests table
        //                             $stockRequest = $this->StockRequests->get($referenceID);

        //                             if ($stockRequest) {
        //                                 $stockRequest->request_status = 'Completed';
        //                                 $this->StockRequests->save($stockRequest);
        //                             }

        //                             // Step 2: Update verify_status in StockMovements table
        //                             $stockMovementsTable = TableRegistry::getTableLocator()->get('StockMovements');
        //                             $stockMovement = $stockMovementsTable->find()
        //                                 ->where(['reference_type' => 'stock_request', 'referenceID' => $referenceID])
        //                                 ->first();

        //                             if ($stockMovement) {
        //                                 $stockMovement->verify_status = 'Approved';
        //                                 $stockMovementsTable->save($stockMovement);
        //                             }

        //                             // Step 3: Update delivery_status to 'Delivered' in SupplierPurchaseOrders table
        //                             $supplierPurchaseOrders = TableRegistry::getTableLocator()->get('SupplierPurchaseOrders');
        //                             $purchaseOrder = $supplierPurchaseOrders->find()
        //                                 ->where(['stock_request_id' => $referenceID])
        //                                 ->first();

        //                             if ($purchaseOrder) {
                                        
        //                                 $purchaseOrder->delivery_status = 'Delivered';

        //                                 // Update supplier_bill_no if available
        //                                 if (!empty($data['supplier_bill_no'])) {
        //                                     $purchaseOrder->supplier_bill_no = $data['supplier_bill_no'];
        //                                 }

        //                                 $supplierPurchaseOrders->save($purchaseOrder);

        //                                 // Step 4: Add missing products to supplier_purchase_order_items
        //                                 $supplierPurchaseOrderItems = TableRegistry::getTableLocator()->get('SupplierPurchaseOrdersItems');

        //                                 $stock_items = $this->StockMovementItems->find()
        //                                     ->where(['stock_movement_id' => $id])
        //                                     ->toArray();

        //                                 if (!empty($purchaseOrder)) {
        //                                     // Step 1: Delete existing supplier purchase order items for this purchase order
        //                                     $supplierPurchaseOrderItems->deleteAll(['supplier_purchase_order_id' => $purchaseOrder->id]);

        //                                     // Step 2: Insert new records
        //                                     foreach ($stock_items as $item) {
        //                                         $newItemData = [
        //                                             'supplier_purchase_order_id' => $purchaseOrder->id,
        //                                             'product_id' => $item['product_id'],
        //                                             'quantity' => $item['quantity'],
        //                                             'approved_quantity' => $item['quantity']
        //                                         ];

        //                                         // Add `product_variant_id` conditionally
        //                                         if (!empty($item['product_variant_id']) && $item['product_variant_id'] !== 'null') {
        //                                             $newItemData['product_variant_id'] = $item['product_variant_id'];
        //                                         }

        //                                         // Add `product_attribute_id` conditionally
        //                                         if (!empty($item['product_attribute_id']) && $item['product_attribute_id'] !== 'null') {
        //                                             $newItemData['product_attribute_id'] = $item['product_attribute_id'];
        //                                         }

        //                                         // Create and save the new entity
        //                                         $newItem = $supplierPurchaseOrderItems->newEntity($newItemData);
        //                                         $supplierPurchaseOrderItems->save($newItem);

        //                                         // Step 4: Update product stocks
        //                                         $this->updateProductStocks(
        //                                             'warehouse',
        //                                             $stock_movement_data->showroom_id ?? null,
        //                                             $stock_movement_data->warehouse_id ?? null,
        //                                             $item->product_id ?? null,
        //                                             !empty($item->product_variant_id) && $item->product_variant_id !== 'null' ? $item->product_variant_id : null,
        //                                             !empty($item->product_attribute_id) && $item->product_attribute_id !== 'null' ? $item->product_attribute_id : null,
        //                                             $item->quantity ?? 0
        //                                         );
        //                                     }
        //                                 }
        //                             }

        //                             $this->incomingStockApprovedEmail($stock_movement_data);
                                    
        //                         } else {
        //                             $this->Flash->error(__('Failed to update stock movement items.'));
        //                         }
                                
        //                     // Successfully updated all items and modifications
        //                     $this->Flash->success(__('The incoming stocks have been approved successfully.'));
        //                 } else {
        //                     $this->Flash->error(__('Failed to approve the incoming stock.'));
        //                 }

        //                 return $this->redirect(['action' => 'index']);
        //             }
        //         }
        //         else
        //         {
        //             $this->Flash->success(__('Failed to approve the stock request.'));

        //             return $this->redirect(['action' => 'index']);
        //         }

        //     } catch (\Exception $e) {
        //         $response['message'] = $e->getMessage();
        //     }

        // }

        if ($this->request->is(['patch', 'post', 'put'])) {

            try {
                $user_detail = $this->Authentication->getIdentity();

                if (!empty($user_detail)) {
                    $role = $this->Roles->find()
                        ->where(['id' => $user_detail->role_id])
                        ->first();

                    if (strtolower($role->name) == 'warehouse manager' || strtolower($role->name) === 'admin') {

                        $data = $this->request->getData();

                        $verified_by = $user_detail->id;
                        $verify_time = date('Y-m-d H:i:s');

                        $product_id = $data['product_id'];
                        $product_variant_id = $data['product_variant_id'];
                        $product_attribute_id = $data['product_attribute_id'];
                        $requested_quantity = $data['quantity'];
                        $accepted_quantity = $data['accepted_quantity'];
                        $statuses = $data['status'];

                        $allRejected = true;

                        $stock_movement_data = $this->StockMovements->get($id);

                        $this->StockMovementItems->deleteAll(['stock_movement_id' => $id]);

                        $stockMovementItems = [];
                        for ($i = 0; $i < sizeof($product_id); $i++) {
                            $status = (isset($statuses[$i]) && $statuses[$i] === 'Reject') ? 'Rejected' : 'Approved';

                            if ($status !== 'Rejected') {
                                $allRejected = false;
                            }

                            $stockMovementItems[] = [
                                'stock_movement_id' => $id,
                                'product_id' => $product_id[$i],
                                'product_variant_id' => !empty($product_variant_id[$i]) ? $product_variant_id[$i] : null,
                                'product_attribute_id' => !empty($product_attribute_id[$i]) ? $product_attribute_id[$i] : null,
                                'quantity' => ($status === 'Rejected') ? $requested_quantity[$i] : $accepted_quantity[$i],
                                'status' => $status
                            ];
                        }

                        $entities = $this->StockMovementItems->newEntities($stockMovementItems);
                        if ($this->StockMovementItems->saveMany($entities)) {

                            if ($allRejected) {
                                $stock_movement_data->verify_status = 'Rejected';
                                $stock_movement_data->verify_time = $verify_time;
                                $stock_movement_data->verified_by = $verified_by;
                                $this->StockMovements->save($stock_movement_data);

                                $this->incomingStockRejectedEmail($stock_movement_data);

                                $this->Flash->success(__('All items were rejected. Stock marked as Rejected and warehouse assistant notified.'));
                                return $this->redirect(['action' => 'index']);
                            }

                            $stock_movement_data->verify_status = 'Approved';
                            $stock_movement_data->verify_time = $verify_time;
                            $stock_movement_data->verified_by = $verified_by;

                            if ($this->StockMovements->save($stock_movement_data)) {

                                $referenceID = $stock_movement_data->referenceID;

                                $stockRequest = $this->StockRequests->get($referenceID);
                                if ($stockRequest) {
                                    $stockRequest->request_status = 'Completed';
                                    $this->StockRequests->save($stockRequest);
                                }

                                $stockMovementsTable = TableRegistry::getTableLocator()->get('StockMovements');
                                $stockMovement = $stockMovementsTable->find()
                                    ->where(['reference_type' => 'stock_request', 'referenceID' => $referenceID])
                                    ->first();

                                if ($stockMovement) {
                                    $stockMovement->verify_status = 'Approved';
                                    $stockMovementsTable->save($stockMovement);
                                }

                                $supplierPurchaseOrders = TableRegistry::getTableLocator()->get('SupplierPurchaseOrders');
                                $purchaseOrder = $supplierPurchaseOrders->find()
                                    ->where(['stock_request_id' => $referenceID])
                                    ->first();

                                if ($purchaseOrder) {
                                    // $purchaseOrder->delivery_status = 'Delivered';

                                    if (!empty($data['supplier_bill_no'])) {
                                        $purchaseOrder->supplier_bill_no = $data['supplier_bill_no'];
                                    }

                                    $supplierPurchaseOrders->save($purchaseOrder);

                                    $supplierPurchaseOrderItems = TableRegistry::getTableLocator()->get('SupplierPurchaseOrdersItems');

                                    $stock_items = $this->StockMovementItems->find()
                                        ->where(['stock_movement_id' => $id])
                                        ->toArray();

                                    $supplierPurchaseOrderItems->deleteAll(['supplier_purchase_order_id' => $purchaseOrder->id]);

                                    foreach ($stock_items as $item) {
                                        $newItemData = [
                                            'supplier_purchase_order_id' => $purchaseOrder->id,
                                            'product_id' => $item['product_id'],
                                            'quantity' => $item['quantity'],
                                            'approved_quantity' => $item['quantity']
                                        ];

                                        if (!empty($item['product_variant_id']) && $item['product_variant_id'] !== 'null') {
                                            $newItemData['product_variant_id'] = $item['product_variant_id'];
                                        }

                                        if (!empty($item['product_attribute_id']) && $item['product_attribute_id'] !== 'null') {
                                            $newItemData['product_attribute_id'] = $item['product_attribute_id'];
                                        }

                                        $newItem = $supplierPurchaseOrderItems->newEntity($newItemData);
                                        $supplierPurchaseOrderItems->save($newItem);

                                        $this->updateProductStocks(
                                            'warehouse',
                                            $stock_movement_data->showroom_id ?? null,
                                            $stock_movement_data->warehouse_id ?? null,
                                            $item->product_id ?? null,
                                            !empty($item->product_variant_id) && $item->product_variant_id !== 'null' ? $item->product_variant_id : null,
                                            !empty($item->product_attribute_id) && $item->product_attribute_id !== 'null' ? $item->product_attribute_id : null,
                                            $item->quantity ?? 0
                                        );
                                    }
                                }

                                $this->incomingStockApprovedEmail($stock_movement_data);
                                $this->Flash->success(__('The incoming stocks have been approved successfully.'));
                            } else {
                                $this->Flash->error(__('Failed to approve the incoming stock.'));
                            }

                        } else {
                            $this->Flash->error(__('Failed to update stock movement items.'));
                        }

                        return $this->redirect(['action' => 'index']);
                    }
                } else {
                    $this->Flash->success(__('Failed to approve the stock request.'));
                    return $this->redirect(['action' => 'index']);
                }

            } catch (\Exception $e) {
                $response['message'] = $e->getMessage();
            }
        }


        $StockMovement = $this->StockMovements->find()
            ->select([
                'StockMovements.id',
                'StockMovements.movement_type',
                'StockMovements.movement_date',
                'StockMovements.image',
                'StockMovements.created',
                'Warehouses.name',
                'StockRequests.supplier_id',        // Fetch supplier_id
                'Suppliers.name',                   // Fetch supplier name
                'SupplierPurchaseOrders.id',        // Purchase order data
                'SupplierPurchaseOrders.order_date',
                'SupplierPurchaseOrders.bill_no',
                'SupplierPurchaseOrders.supplier_bill_no',
                'SupplierPurchaseOrders.payment_status',
                'SupplierPurchaseOrders.delivery_status',
                'SupplierPurchaseOrders.required_delivery_date',
                'SupplierPurchaseOrders.payment_due_date',
            ])
            ->contain(['Warehouses'])
            ->leftJoin(
                ['StockRequests' => 'stock_requests'],
                'StockMovements.referenceID = StockRequests.id'
            )
            ->leftJoin(
                ['Suppliers' => 'suppliers'],
                'StockRequests.supplier_id = Suppliers.id'
            )
            ->leftJoin(
                ['SupplierPurchaseOrders' => 'supplier_purchase_orders'],
                'SupplierPurchaseOrders.stock_request_id = StockRequests.id'
            )
            ->where(['StockMovements.id' => $id])
            ->first();

        $supplierId = $StockMovement['StockRequests']['supplier_id'] ?? null;

        // Step 2: Fetch StockMovementItems and supplier_price using supplier_id
        $StockMovementItems = $this->StockMovements->StockMovementItems->find()
            ->select([
                'StockMovementItems.id',
                'StockMovementItems.quantity',
                'StockMovementItems.product_id',
                'StockMovementItems.product_variant_id',
                'StockMovementItems.product_attribute_id',
                'StockMovementItems.stock_movement_id',
                'Products.name',
                'Products.purchase_price', 
                'Products.sku', 
                'ProductVariants.id', 
                'ProductVariants.variant_name', 
                'ProductVariants.purchase_price', 
                'ProductVariants.sku',
            ])
            ->leftJoinWith('Products')  
            ->leftJoinWith('ProductVariants')
            ->where([
                'StockMovementItems.stock_movement_id' => $id
            ])
            ->toArray();

        foreach ($StockMovementItems as &$item) {

            $item->supplier_price = null;

            // Check if product_variant_id exists and fetch supplier_price accordingly
            if ($item->product_variant_id) {

                $supplierPriceQuery = $this->SupplierProducts->find()
                    ->select(['supplier_price'])
                    ->where([
                        'SupplierProducts.supplier_id' => $supplierId,
                        'SupplierProducts.product_id' => $item->product_id,
                        'SupplierProducts.product_variant_id' => $item->product_variant_id,
                    ])
                    ->first();
            } else {
                $supplierPriceQuery = $this->SupplierProducts->find()
                    ->select(['supplier_price'])
                    ->where([
                        'SupplierProducts.supplier_id' => $supplierId,
                        'SupplierProducts.product_id' => $item->product_id,
                    ])
                    ->first();
            }

            // Assign supplier_price if found
            if ($supplierPriceQuery) {
                $item->supplier_price = $supplierPriceQuery->supplier_price;
            }

            // Initialize an attributes array in each item
            $item->attributes = [];

            if ($item->product_attribute_id) {
                // Fetch attributes related to the product
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $item->product_attribute_id])
                    ->contain([
                        'Attributes' => [
                            'fields' => ['Attributes.name']
                        ],
                        'AttributeValues' => [
                            'fields' => ['AttributeValues.value']
                        ]
                    ])
                    ->first();

                if ($attributes) {
                    // Add attribute details to the item if found
                    $item->attributes = [
                        'attribute_name' => $attributes->attribute->name ?? '',
                        'attribute_value' => $attributes->attribute_value->value ?? ''
                    ];
                }
            }
        }

        if($supplierId)
        {
            $products = $this->Products->find()
                ->distinct(['Products.id'])
                ->innerJoinWith('SupplierProducts') // Association name
                ->where([
                    'SupplierProducts.status' => 'A',
                    'SupplierProducts.supplier_id' => $supplierId
                ])
                ->order(['Products.name' => 'ASC'])
                ->toArray();
        }
        else
        {
            $products = $this->Products->find()
                ->where(['Products.status' => 'A'])
                ->order(['Products.name' => 'ASC'])
                ->toArray();
        }

        $this->set(compact('StockMovement', 'StockMovementItems', 'products'));

    }

    protected function updateProductStocks($requestTo, $showroom_id, $warehouse_id, $product_id, $product_variant_id, $product_attribute_id, $accepted_quantity)
    {
        // Initialize conditions with product_id
        $conditions = [
            'product_id' => $product_id,
        ];

        // Add product_variant_id only if it's not null
        if ($product_variant_id) {
            $conditions['product_variant_id'] = $product_variant_id;
        }

        if ($product_attribute_id) {
            $conditions['product_attribute_id'] = $product_attribute_id;
        }

        // Add either showroom_id or warehouse_id condition
        if ($requestTo === 'showroom' && $showroom_id) {
            $conditions['showroom_id'] = $showroom_id;
        } elseif ($requestTo === 'warehouse' && $warehouse_id) {
            $conditions['warehouse_id'] = $warehouse_id;
        }

        // Fetch product stock
        $stockExist = $this->ProductStocks->find()
            ->where($conditions)
            ->first();

        if ($stockExist) {
            // Update existing stock
            $productStockID = $stockExist->id;
            $existQuantity = $stockExist->quantity;
            $increaseQuantity = $existQuantity + $accepted_quantity;

            // Update only the quantity
            $updateData = ['quantity' => $increaseQuantity];

            // Fetch the stock record and update
            $updateStock = $this->ProductStocks->get($productStockID);
            $updateStock->quantity = $increaseQuantity;

            // Save the updated stock
            $this->ProductStocks->save($updateStock);
        } else {
            // Prepare data for new stock entry
            $newStockData = [
                'product_id' => $product_id,
                'quantity' => $accepted_quantity
            ];

            if ($product_variant_id) {
                $newStockData['product_variant_id'] = $product_variant_id;
            }

            if ($product_attribute_id) {
                $newStockData['product_attribute_id'] = $product_attribute_id;
            }

            if ($requestTo === 'showroom') {
                $newStockData['showroom_id'] = $showroom_id;
            } elseif ($requestTo === 'warehouse') {
                $newStockData['warehouse_id'] = $warehouse_id;
            }

            // Insert new stock record
            $this->ProductStocks->addProductStock($requestTo, $warehouse_id, $newStockData);
        }
    }

    private function incomingStockApprovedEmail($stockMovement)
    {
        $toEmails = [];

        $warehouse = $this->Warehouses->get($stockMovement->warehouse_id, [
                'contain' => ['Assistants']
            ]);

        $warehouseName = $warehouse && $warehouse->assistant ? $warehouse->assistant->first_name.' '.$warehouse->assistant->last_name : 'Unknown Assistant';
        $warehouseAssistantEmail = $warehouse && $warehouse->assistant ? $warehouse->assistant->email : null;

        $toEmails = array_filter([$warehouseAssistantEmail]); // Remove null values

        // Ensure at least one recipient exists
        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipients found for incoming stock approval request ID: " . $stockMovement->id);
            return;
        }

        // Format request date as DD-MM-YY
        $formattedDate = $stockMovement->modified ? $stockMovement->modified->format('d-m-y') : 'N/A';

        // Set up email variables
        $emailData = [
            'stock_request_id' => $stockMovement->referenceID,
            'warehouse_name' => $warehouse->name ?? 'N/A',
            'greeting' => "Dear {$warehouseName},",
            'message' => "An incoming stock has been successfully added to the warehouse by the manager.",
            'request_date' => $formattedDate,
            'status' => 'Approved'
        ];

        $subject = "Incoming Stock Added - #{$stockMovement->referenceID}";

        // Use the global email sending function
        $this->Global->send_email(
            $toEmails, // Send to the appropriate recipients
            null, // Default FROM email from settings
            $subject,
            'incoming_stock_approved', // Email template name
            $emailData
        );
    }

    private function incomingStockRejectedEmail($stockMovement)
    {
        $toEmails = [];

        $warehouse = $this->Warehouses->get($stockMovement->warehouse_id, [
            'contain' => ['Assistants']
        ]);

        $warehouseName = $warehouse && $warehouse->assistant ? $warehouse->assistant->first_name.' '.$warehouse->assistant->last_name : 'Unknown Assistant';
        $warehouseAssistantEmail = $warehouse && $warehouse->assistant ? $warehouse->assistant->email : null;

        $toEmails = array_filter([$warehouseAssistantEmail]);
        if (empty($toEmails)) {
            \Cake\Log\Log::warning("No valid recipients found for REJECTED incoming stock request ID: " . $stockMovement->id);
            return;
        }

        $formattedDate = $stockMovement->modified ? $stockMovement->modified->format('d-m-y') : 'N/A';

        $emailData = [
            'stock_request_id' => $stockMovement->referenceID,
            'warehouse_name' => $warehouse->name ?? 'N/A',
            'greeting' => "Dear {$warehouseName},",
            'message' => "The incoming stock request was rejected by the warehouse manager.",
            'request_date' => $formattedDate,
            'status' => 'Rejected'
        ];

        $subject = "Incoming Stock Rejected - #{$stockMovement->referenceID}";

        $this->Global->send_email(
            $toEmails,
            null,
            $subject,
            'incoming_stock_approved', // Reuse same template
            $emailData
        );
    }

    //Add incoming stock based on BL
    public function addIncomingStock($id)
    {
        try{
        $this->loadComponent( 'Stock'); // Load the Stock component
        if ($this->request->is('get')) {
            $Bl = $this->BL->getBLById($id);
            $qrLists = $this->QrList->getByBlId($Bl->id)->toArray();
            $supplier = $this->Suppliers->getSupplierById($Bl->supplier_id);
            $stock_request = $this->StockRequests->getStockRequestById($Bl->stock_request_id);
            $BLItemsarr= $this->BLItems->getBLItemsByBLId($id);
            
            $stockMovement = $this->StockMovements->getByBLNo($id);
            Log::debug("Stock Movement");
            Log::debug(print_r($stockMovement, true));
            if($stockMovement)
            {
                $stockMovementItems = $this->StockMovementItems->getByStockMovementId($stockMovement->id);
                $BLItems[] = $stockMovementItems;

                foreach($stockMovementItems as $item)
                {
                    if(!is_null($item->product_variant_id) && $item->product_variant_id != 0 )
                    {
                        $variants[] = $this->ProductVariants->getVariantById($item->product_variant_id);
                    }
                    else
                    {
                        $variants[] = [];
                    }
                    if(!is_null($item->product_attribute_id) && $item->product_attribute_id != 0)
                    {
                        $attributes[] = $this->ProductAttributes->getAttributeById($item->product_attribute_id);
                    }
                    else
                    {
                        $attributes[] = [];
                    }
                
                    // if($item->product_variant_id)
                    $products[] = $this->Products->getProductById($item->product_id);

                }
                
                   
            }
                foreach($BLItemsarr as $bitem)
                {
                    if(!is_null($bitem->product_variant_id))
                    {
                        $variants[] = $this->ProductVariants->getVariantById($bitem->product_variant_id);
                    }
                    else
                    {
                        $variants[] = [];
                    }

                    if(!is_null($bitem->product_attribute_id))
                    {
                        $attributes[] = $this->ProductAttributes->getAttributeById($bitem->product_attribute_id);
                    }
                    else
                    {
                        $attributes[] = [];
                    }
                
                    // if($item->product_variant_id)
                    $products[] = $this->Products->getProductById($bitem->product_id);

                }

        }
        if ($this->request->is('post')) {


                $data = $this->request->getData();
                Log::debug("BL DATA :: ");
                Log::debug(print_r($data, true));
                $bl_id = $data['bl_no'];
                $Bl = $this->BL->getBLById($bl_id);
                $first_product_count = $data['first_product_count'];
                // $item->status = 'Approved';
                // $this->BLItems->save($item);

            $requested_user = $this->Authentication->getIdentity();

            if (!empty($requested_user)) {
                $role = $this->Roles->get($requested_user->role_id);
            }

            $allowedFormats = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_TYPE');
            $maxWebImageSize = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_SIZE') * 1024 * 1024;
            if (!empty($data['document']) && $data['document']->getError() === UPLOAD_ERR_OK) {
                $web_image = $data['document'];
                $webImageName = trim($web_image->getClientFilename());
                $webImageSize = $web_image->getSize();
                $webImageExt = strtolower(pathinfo($webImageName, PATHINFO_EXTENSION));

                if (!in_array($webImageExt, $allowedFormats)) {
                    return $this->Flash->error(__('Invalid file type for web image. Only ' . $allowedFormats . ' are allowed.'));
                }

                if ($webImageSize > $maxWebImageSize) {
                    return $this->Flash->error(__('Web image size exceeds the maximum allowed size of ' . $maxWebImageSize . ' MB.'));
                }

                list($width, $height) = getimagesize($web_image->getStream()->getMetadata('uri'));
                $minWidth = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MIN_WIDTH');
                $maxWidth = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MAX_WIDTH');
                $minHeight = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MIN_HEIGHT');
                $maxHeight = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MAX_HEIGHT');

                if (!empty($webImageName)) {
                    $webImageTmpName = $web_image->getStream()->getMetadata('uri');
                    $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));
                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                    $filePath = Configure::read('Constants.INCOMING_STOCK_DOCUMENT');
                    $folderPath = $uploadFolder . $filePath;
                    $targetdir = WWW_ROOT . $folderPath;
                    $webImageFile = pathinfo($webImageName, PATHINFO_FILENAME) . '_' . $rand . '.' . $webImageExt;
                    $uploadResult = $this->Media->upload($webImageTmpName, $targetdir, $webImageFile, $uploadFolder);
                    if ($uploadResult === 'Success') {
                        $data['document'] = $uploadFolder . $webImageFile;
                    }
                }
            }
            else
            {
                $data['document'] = "";
            }

            $stockRequestId = $data['stock_request_id'];
            $movementDate = $data['movement_date'];
            $document = $data['document'];
            $stockMovementId= $data['stock_movement_id'];
            $stockMovementItemId = $data['stock_movement_item_id'];
            $blItemId = $data['bl_item_id'];


            // // Fetch stock request details
            $stockRequest = $this->StockRequests->find()
                ->where(['StockRequests.id' => $stockRequestId])
                ->contain(['StockRequestItems'])
                ->first();

            if (!$stockRequest) {
                $this->Flash->error(__('The stock request not found.'));
            }
            $warehouse = $this->Warehouses->getWarehousesByManagerId($requested_user->id);

            // Prepare parameters for the Stock component
            $warehouse_id = $warehouse ->id;
            $movement_date = $movementDate;
            $reference_type = 'stock_request';
            $referenceID = $stockRequestId;
            $image = $document;
           
            

            $product_id = $data['product_id'];
            $product_variant_id = $data['product_variant_id'];
            $product_attribute_id = $data['product_attribute_id'];
            $remaining_quantity = $data['remaining_quantity'];
            $quantity = $data['product_quantity'];
            $defective_quantity = $data['defective_quantity'];
            $new_remaining_quantity = $remaining_quantity - $quantity + $defective_quantity;
            $first_product_count = (int)$first_product_count + (int)$quantity;
            $stock_items = [];
            // for($i=0; $i < sizeof($product_id); $i++)
            // {
                $stock_items[] = [
                    'product_id' => $product_id,
                    'product_variant_id' => $product_variant_id,
                    'product_attribute_id' => $product_attribute_id,
                    'remaining_quantity' => $new_remaining_quantity,  
                    'quantity' => $quantity,
                    'defective_quantity' => $defective_quantity,
                    'first_product_count' => $first_product_count,
                ];

            // }
            // Call the Stock component's method
            // $result = $this->Stock->addWarehouseInStock($warehouse_id, $movement_date, $reference_type, $referenceID, $image, $stock_items, $driver_id = null);
            // Check user role before calling Stock component
            if (strtolower($role->name) === 'warehouse manager' || strtolower($role->name) === 'admin') {
                

                $result = $this->Stock->addWarehouseInStock($warehouse_id, $movementDate, $reference_type, $referenceID, $image, $stock_items, $stockMovementId,$stockMovementItemId, $Bl,$blItemId, $driver_id=null);
                Log::debug("Stock Items :: ");
Log::debug("Stock Items :: ");

Log::debug(print_r($result, true));
                 

                

            } elseif (strtolower($role->name) === 'warehouse assistant') {
                // Call a modified version that only inserts into StockMovements and StockMovementItems
                
                    $result = $this->Stock->addWarehouseInStockWithoutUpdatingProductStock($warehouse_id, $movementDate, $reference_type, $referenceID, $image, $stock_items, $driver_id = null);

                    if($result)
                    {
                        $this->sendIncomingStockApprovalEmail($stockRequest);
                    }
                
            }

            // Return the result as JSON
            if ($result) {
Log::debug("ITEM COUNT IN RESULT". $first_product_count);
                // Step 1: Update request_status to 'Completed' in StockRequests table
                $stockRequest = $this->StockRequests->get($referenceID);
                if (strtolower($role->name) === 'warehouse manager' || strtolower($role->name) === 'admin') {
                    
                    if ($stockRequest) {

                        // $stockRequest->request_status = 'Completed';
                        $this->StockRequests->save($stockRequest);
                    }
Log::debug("Reference ID".$referenceID);
Log::debug(print_r($stockRequest, true));
                    // Step 2: Update verify_status in StockMovements table
                    $stockMovementsTable = TableRegistry::getTableLocator()->get('StockMovements');
                    $stockMovement = $stockMovementsTable->find()
                        ->where(['reference_type' => 'stock_request', 'referenceID' => $referenceID])
                        ->first();

                    if ($stockMovement) {
                        $stockMovement->verify_status = 'Approved';
                        $stockMovementsTable->save($stockMovement);
                    }

                }

                // Step 3: Update delivery_status to 'Delivered' in SupplierPurchaseOrders table
                $supplierPurchaseOrders = TableRegistry::getTableLocator()->get('SupplierPurchaseOrders');

                $purchaseOrder = $supplierPurchaseOrders->find()
                    ->where(['stock_request_id' => $referenceID])
                    ->first();
                

                if ($purchaseOrder) {

                    if (strtolower($role->name) === 'warehouse manager' || strtolower($role->name) === 'admin') {
                        // $purchaseOrder->delivery_status = 'Delivered';
                    }

                    // Update supplier_bill_no if available
                    if (!empty($data['supplier_bill_no'])) {
                        $purchaseOrder->supplier_bill_no = $data['supplier_bill_no'];
                    }

                    $supplierPurchaseOrders->save($purchaseOrder);
                }

                // Add missing products to supplier_purchase_order_items
                $supplierPurchaseOrderItems = TableRegistry::getTableLocator()->get('SupplierPurchaseOrdersItems');
                foreach ($stock_items as $item) {
                    if($purchaseOrder) {
                        $existsConditions = [
                            'supplier_purchase_order_id' => $purchaseOrder->id,
                            'product_id' => $item['product_id']
                        ];
                    }   

                    // Add `product_variant_id` conditionally
                    if (!empty($item['product_variant_id']) && $item['product_variant_id'] !== 'null') {
                        $existsConditions['product_variant_id'] = $item['product_variant_id'];
                    }

                    // Add `product_attribute_id` conditionally
                    if (!empty($item['product_attribute_id']) && $item['product_attribute_id'] !== 'null') {
                        $existsConditions['product_attribute_id'] = $item['product_attribute_id'];
                    }

                    $exists = $supplierPurchaseOrderItems->exists($existsConditions);

                    if (!$exists) {

                        $newItemData = [
                            'supplier_purchase_order_id' => $purchaseOrder->id,
                            'product_id' => $item['product_id'],
                            'quantity' => $item['quantity'],
                            'approved_quantity' => $item['quantity']
                        ];

                        // Add `product_variant_id` conditionally
                        if (!empty($item['product_variant_id']) && $item['product_variant_id'] !== 'null') {
                            $newItemData['product_variant_id'] = $item['product_variant_id'];
                        }

                        // Add `product_attribute_id` conditionally
                        if (!empty($item['product_attribute_id']) && $item['product_attribute_id'] !== 'null') {
                            $newItemData['product_attribute_id'] = $item['product_attribute_id'];
                        }

                        // Create and save the new entity
                        $newItem = $supplierPurchaseOrderItems->newEntity($newItemData);
                        $supplierPurchaseOrderItems->save($newItem);
                    }
                }

                $this->Flash->success(__('The stocks have been saved.'));
                $resultdata = [
                    'status' => 'success',
                    'message' => 'Stock updated successfully.',
                    'data' => $result // or any relevant payload
                ];
                $BLitem = $this->BLItems->getBLItemById($data['bl_item_id']);
                $BLitem->status = "Approved";
                $this->BLItems->save($BLitem);
                $result['new_remaining_quantity'] = $new_remaining_quantity;
                $result['first_product_count'] = $first_product_count;
                return $this->response
                    ->withType('application/json')
                    ->withStringBody(json_encode($result));
                                // return $this->redirect(['action' => 'index']);
            
            } else {
                return false; 
                // $this->Flash->error(__('Failed to process stock request. Please, try again.'));
            
            }
        }

        // Get the currently authenticated user
        $requested_user = $this->Authentication->getIdentity();

        if (!empty($requested_user)) {
            // Get the role of the user
            $role = $this->Roles->get($requested_user->role_id);

            // Check if the user's role is "Warehouse Manager"
            if (strtolower($role->name) === 'warehouse manager' || strtolower($role->name) === 'warehouse assistant') {
                
                // Get the warehouse_id for the manager
                if(strtolower($role->name) === 'warehouse manager')
                {
                    // Get the warehouse_id for the manager
                    $warehouse = $this->Warehouses->find()
                        ->select(['id'])
                        ->where(['manager_id' => $requested_user->id])
                        ->first();
                }
                else if(strtolower($role->name) === 'warehouse assistant')
                {
                    // Get the warehouse_id for the manager
                    $warehouse = $this->Warehouses->find()
                        ->select(['id'])
                        ->where(['assistant_id' => $requested_user->id])
                        ->first();
                }

                if (!empty($warehouse)) {
                    $warehouseId = $warehouse->id;

                    // $suppliers = $this->Suppliers->find()
                    //     ->where(['Suppliers.status' => 'A'])
                    //     ->contain([
                    //         'SupplierPurchaseOrders' => function ($q) use ($warehouseId) {
                    //             $subquery = $this->StockMovements->find()
                    //                 ->select(['referenceID'])
                    //                 ->where(['referenceID IS NOT' => null]); // Ensure NULL values are not checked
                                
                    //             return $q->select([
                    //                     'SupplierPurchaseOrders.supplier_id', 
                    //                     'SupplierPurchaseOrders.bill_no', 
                    //                     'SupplierPurchaseOrders.supplier_bill_no', 
                    //                     'SupplierPurchaseOrders.stock_request_id'
                    //                 ])
                    //                 ->where([
                    //                     'SupplierPurchaseOrders.delivery_status' => 'Pending',
                    //                     'SupplierPurchaseOrders.status' => 'A',
                    //                     'SupplierPurchaseOrders.id_deliver_to' => $warehouseId,
                    //                     'SupplierPurchaseOrders.stock_request_id NOT IN' => $subquery
                    //                 ]);
                    //         }
                    //     ])
                    //     ->toArray();





                        $subquery = $this->StockMovementItems->find()
    ->select(['stock_movement_id'])
    ->where(['StockMovementItems.status !=' => 'Completed']);

$suppliers = $this->Suppliers->find()
    ->where(['Suppliers.status' => 'A'])
    ->contain([
        'SupplierPurchaseOrders' => function ($q) use ($warehouseId, $subquery) {
            return $q->select([
                    'SupplierPurchaseOrders.id',
                    'SupplierPurchaseOrders.supplier_id',
                    'SupplierPurchaseOrders.bill_no',
                    'SupplierPurchaseOrders.supplier_bill_no',
                    'SupplierPurchaseOrders.stock_request_id'
                ])
                ->where([
                    'SupplierPurchaseOrders.delivery_status' => 'Pending',
                    'SupplierPurchaseOrders.status' => 'A',
                    'SupplierPurchaseOrders.id_deliver_to' => $warehouseId,
                    'SupplierPurchaseOrders.id IN' => $subquery
                ]);
        }
    ])
    ->toArray();


                } else {
                    // Handle case where no warehouse is found for the manager
                    $suppliers = []; // Or any appropriate action
                }
            } else {

                $suppliers = $this->Suppliers->find()
                    ->where(['Suppliers.status' => 'A'])
                    ->contain([
                         
                    ])
                    ->toArray(); 

            }
        }

        // $requested_user = $this->Authentication->getIdentity();

        $imageSize = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_SIZE');
        $webImageMinWidth = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MIN_WIDTH');
        $webImageMaxWidth = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MAX_WIDTH');
        $webImageMinHeight = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MIN_HEIGHT');
        $webImageMaxHeight = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MAX_HEIGHT');
        $webImageType = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_IMAGE_TYPE');
        $webImageTypeText = Configure::read('Constants.INCOMING_STOCK_DOCUMENT_TEXT_TYPE');

        $file_acceptance_msg = __('Only '.$webImageTypeText.' files are allowed.'.$imageSize.'MB.');

        $this->set([
            'webImageSize' => Configure::read('Constants.INCOMING_STOCK_DOCUMENT_SIZE'),
            'webImageType' => array_map('trim', explode(',', $webImageType)),
            'webImageMinWidth' => Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MIN_WIDTH'),
            'webImageMaxWidth' => Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MAX_WIDTH'),
            'webImageMinHeight' => Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MIN_HEIGHT'),
            'webImageMaxHeight' => Configure::read('Constants.INCOMING_STOCK_DOCUMENT_MAX_HEIGHT'),
        ]);
        }

        catch (\Exception $e) {
                $response['message'] = $e->getMessage();
                return $response;
            }
            if(isset($BLItems) && !empty($BLItems))
            {
                $itemarr = $BLItems[0]->toArray();
            }
            else
            {
                $itemarr = [];
            }
            // $itemarr = $BLItems[0]->toArray();
            $finalitems = array_merge($itemarr, $BLItemsarr);
            $BLItems = $finalitems;
            
        $this->set(compact('suppliers', 'file_acceptance_msg', 'supplier', 'BLItems', 'Bl','products','variants','attributes','qrLists')); 

    }

    //Show list of all product items as per quantity and their unique id and barcode
    public function itemList($item_id,$first_product_count=null,$quantity=null)
    {   
       
        $stockitems = $this->ProductItems->getByStockMovemenetItemId($item_id,$first_product_count,$quantity);
        $this->set(compact('stockitems'));
        

    }
}
