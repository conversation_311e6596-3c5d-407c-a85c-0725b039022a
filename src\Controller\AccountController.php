<?php
declare(strict_types=1);

namespace App\Controller;

use Cake\Controller\Controller;
use Cake\ORM\TableRegistry;
use Cake\Routing\Router;
use Cake\Core\Configure;
use Cake\Utility\Security;
use Cake\I18n\Time;
use Cake\I18n\FrozenTime;
use App\Mailer\UserMailer;
use Authentication\PasswordHasher\DefaultPasswordHasher;
use Cake\Http\Client;
use Cake\Http\Exception\BadRequestException;
use Cake\Http\Response;
use Cake\Utility\Text;
use DateTime;
use Mpdf\Mpdf;
use Cake\Log\Log;


/**
 * Users Controller
 *
 */
class AccountController extends Controller
{
    protected $Cities;
    protected $Categories;
    protected $Users;
    protected $Customers;
    protected $OtpVerifications;
    protected $Banners;
    protected $BannerAds;
    protected $Offers;
    protected $Widgets;
    protected $Products;
    protected $ApiRequestLogs;
    protected $PaymentMethods;
    protected $Carts;
    protected $CartItems;
    protected $WidgetCategoryMappings;
    protected $Orders;
    protected $OrderItems;
    protected $SiteSettings;
    protected $Wishlists;
    protected $Reviews;
    protected $Showrooms;
    protected $Brands;
    protected $CustomerAddresses;
    protected $Loyalty;
    protected $LoyaltySettings;
    protected $ReviewImages;
    protected $ContentPages;
    protected $CustomerCards;
    protected $Invoices;
    protected $DeliveryCharges;
    protected $Transactions;
    protected $FaqCategories;
    protected $Faqs;
    protected $Wallets;
    protected $OrderCancellationCategories;
    protected $OrderCancellations;
    protected $OrderReturnCategories;
    protected $OrderReturns;
    protected $OrderTrackingHistories;
    protected $ProductImages;
    protected $OrderReturnImages;
    protected $ContactQueryTypes;
    protected $CartItemAttributes;
    protected $CreditApplications;
    protected $CartCreditPayments;
    protected $ZohoSettings;
    protected $zohoSettings;
    protected $ProductPaymentSettings;
    protected $Partners;

    public function initialize(): void
    {
        parent::initialize();

        $this->CartCreditPayments = $this->fetchTable('CartCreditPayments');
        $this->CreditApplications = $this->fetchTable('CreditApplications');

        $this->CartItemAttributes = $this->fetchTable('CartItemAttributes');
        $this->Partners = $this->fetchTable('Partners');
        $this->Cities = $this->fetchTable('Cities');
        $this->Categories = $this->fetchTable('Categories');
        $this->Users = $this->fetchTable('Users');
        $this->Customers = $this->fetchTable('Customers');
        $this->OtpVerifications = $this->fetchTable('OtpVerifications');
        $this->Banners = $this->fetchTable('Banners');
        $this->BannerAds = $this->fetchTable('BannerAds');
        $this->Offers = $this->fetchTable('Offers');
        $this->Widgets = $this->fetchTable('Widgets');
        $this->Products = $this->fetchTable('Products');
        $this->Reviews = $this->fetchTable('Reviews');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Brands = $this->fetchTable('Brands');
        $this->CustomerAddresses = $this->fetchTable('CustomerAddresses');
        $this->ApiRequestLogs = $this->fetchTable('ApiRequestLogs');
        $this->PaymentMethods = $this->fetchTable('PaymentMethods');
        $this->Carts = $this->fetchTable('Carts');
        $this->CartItems = $this->fetchTable('CartItems');
        $this->WidgetCategoryMappings = $this->fetchTable('WidgetCategoryMappings');
        $this->Orders = $this->fetchTable('Orders');
        $this->OrderItems = $this->fetchTable('OrderItems');
        $this->SiteSettings = $this->fetchTable('SiteSettings');
        $this->Wishlists = $this->fetchTable('Wishlists');
        $this->Loyalty = $this->fetchTable('Loyalty');
        $this->LoyaltySettings = $this->fetchTable('LoyaltySettings');
        $this->ReviewImages = $this->fetchTable('ReviewImages');
        $this->ContentPages = $this->fetchTable('ContentPages');
        $this->DeliveryCharges = $this->fetchTable('DeliveryCharges');
        $this->Transactions = $this->fetchTable('Transactions');
        $this->CustomerCards = $this->fetchTable('CustomerCards');
        $this->Invoices = $this->fetchTable('Invoices');
        $this->FaqCategories = $this->fetchTable('FaqCategories');
        $this->Faqs = $this->fetchTable('Faqs');
        $this->Wallets = $this->fetchTable('Wallets');
        $this->OrderCancellationCategories = $this->fetchTable('OrderCancellationCategories');
        $this->OrderCancellations = $this->fetchTable('OrderCancellations');
        $this->OrderReturnCategories = $this->fetchTable('OrderReturnCategories');
        $this->OrderReturns = $this->fetchTable('OrderReturns');
        $this->OrderTrackingHistories = $this->fetchTable('OrderTrackingHistories');
        $this->ProductImages = $this->fetchTable('ProductImages');
        $this->OrderReturnImages = $this->fetchTable('OrderReturnImages');
        $this->ContactQueryTypes = $this->fetchTable('ContactQueryTypes');
        $this->ZohoSettings = $this->fetchTable('ZohoSettings');
        $this->ProductPaymentSettings = $this->fetchTable('ProductPaymentSettings');

        $this->loadComponent('Global');
        $this->loadComponent('Media');
        $this->loadComponent('CustomPaginator');
        $this->loadComponent('Website');
        $this->loadComponent('Flash');
        $this->loadComponent('Mtn');
        $this->loadComponent('Wave');
        $this->loadComponent('WebsiteFunction');
        $this->loadComponent('Zoho');
        $this->viewBuilder()->setLayout('web');

    }


    public function beforeRender(\Cake\Event\EventInterface $event)
    {
        parent::beforeRender($event);
        $topSellCarEmptyItemShow = $this->WebsiteFunction->getTopSellItems(10);
        $scrollContentArr = $this->WebsiteFunction->getHomeScrollText();

        // Pass it to all views
        $this->set(compact('topSellCarEmptyItemShow','scrollContentArr'));
    }

    public function myAccount()
    {
        if (empty($this->request->getSession()->read('Auth.User'))) {
            $this->Flash->toast(__("Login required to access user account."), [
                'element' => 'toast',
                'params' => ['type' => 'warning']
            ]);
            return $this->redirect(['controller' => 'customer', 'action' => 'login']);
        }
        $account = $this->request->getSession()->read('Auth.User');
        $users = $this->Users->find()
            ->contain(['Customers']) // Include related Customers
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $account->id])
            ->first();
        if ($users->customer->profile_photo) {
            $users->customer->profile_photo = $this->Media->getCloudFrontURL($users->customer->profile_photo);
        }
        $loyaltyDetails = $this->Loyalty->calculateLoyaltyPoints($users->customer->id);
        $walletAmount = $this->Wallets->getMyWalletAmount($users->customer->id);
        if ($walletAmount) {
            $walletAmount = $walletAmount->balance;
        } else {
            $walletAmount = 0;
        }
        $this->set(compact('users','loyaltyDetails', 'walletAmount'));
        $this->viewBuilder()->setTemplatePath('account');
        $this->render('account');
    }
    public function changePassword()
    {
        if ($this->request->is('post')) {
            $data = $this->request->getData();
            $identity = $this->request->getSession()->read('Auth.User.id');

            if (!$identity) {
                $this->Flash->websiteError(__('User is not authenticated'));
                return $this->redirect(['controller' => 'Account', 'action' => 'myAccount']);
            }

            $user = $this->Users->find()
                ->where(['id' => $identity, 'status' => 'A'])
                ->first();

            if (!$user) {
                $this->Flash->websiteError(__('User not found or inactive.'));
                return $this->redirect(['controller' => 'Account', 'action' => 'myAccount']);
            }

            $oldPassword = $user->password ?? ''; // If password is null, set it as an empty string

            // Skip old password validation if the stored password is empty/null
            if (!empty($user->password) && empty($data['old_password'])) {
                $this->Flash->websiteError(__('Old password is required.'));
                return $this->redirect(['controller' => 'Account', 'action' => 'myAccount']);
            }

            if (empty($data['new_password']) || empty($data['confirm_password'])) {
                $this->Flash->websiteError(__('New password fields cannot be empty.'));
                return $this->redirect(['controller' => 'Account', 'action' => 'myAccount']);
            }

            if ($data['new_password'] !== $data['confirm_password']) {
                $this->Flash->websiteError(__('New password and confirm password do not match'));
                return $this->redirect(['controller' => 'Account', 'action' => 'myAccount']);
            }

            // Call password change function, skipping old password check if it's empty in DB
            $changePasswordResult = $this->Users->changeUserPassword(
                $identity,
                !empty($user->password) ? $data['old_password'] : '', // Check old password only if it's set
                $data['new_password']
            );

            if ($changePasswordResult['status'] === 'success') {
                $this->Flash->toast(__("Password changed successfully."), [
                    'element' => 'toast',
                    'params' => ['type' => 'success']
                ]);
            } else {
                $this->Flash->websiteError(__('Old password is incorrect or signed in with social login.'));
            }

            return $this->redirect(['controller' => 'Account', 'action' => 'myAccount']);
        }

        $this->Flash->websiteError(__('Method not allowed'));
        return $this->redirect(['controller' => 'Account', 'action' => 'myAccount']);
    }


    public function checkPassword($storedPassword, $inputPassword)
    {
        $hasher = new \Cake\Auth\DefaultPasswordHasher();
        return $hasher->check($inputPassword, $storedPassword);
    }

    public function updateProfilePhoto()
    {

        $data = $this->request->getData();

        $profile_photo = $data['profile_photo'];
        $fileName = trim($profile_photo->getClientFilename());

        if (!empty($fileName)) {
            $imageTmpName = $profile_photo->getStream()->getMetadata('uri');
            $rand = strtoupper(substr(uniqid(sha1((string)time()), true), -5));
            $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
            $folderPath = $uploadFolder;
            $targetdir = WWW_ROOT . $folderPath;
            $ext = pathinfo($fileName, PATHINFO_EXTENSION);
            $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

            $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);

            if ($uploadResult !== 'Success') {
                $this->Flash->toast(__('Something went wrong while uploading your image. Please try again.'), [
                    'element' => 'toast',
                    'params' => ['type' => 'warning']
                ]);
            } else {
                $userId = $this->request->getSession()->read('Auth.User')['id'];
                $customer = $this->Customers->find('all')->where(['user_id' => $userId])->first();
                if ($customer) {
                    $customerData = [
                        'profile_photo' => $folderPath . $imageFile
                    ];
                    $customer = $this->Customers->patchEntity($customer, $customerData);
                    $this->Customers->save($customer);
                }
                //  $customer_attributes['profile_photo'] = $folderPath . $imageFile;
                $this->Flash->toast(__('Profile Image Updated Successfully'), [
                    'element' => 'toast',
                    'params' => ['type' => 'success']
                ]);
            }
        } else {
            $this->Flash->toast(__('Choose Image to Upload.'), [
                'element' => 'toast',
                'params' => ['type' => 'warning']
            ]);
        }
        return $this->redirect(['controller' => 'Account', 'action' => 'myAccount']);
    }

    public function updateMyAccount()
    {
        if ($this->request->is('post')) {

            $userId = $this->request->getSession()->read('Auth.User')['id'];
            $user = $this->Users->get($userId, [
                'contain' => ['Customers']
            ]);
            $data = $this->request->getData();
            $userInfo = array();
          
            // Validate email
            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {

                $this->Flash->toast(__('Invalid email address.'), [
                    'element' => 'toast',
                    'params' => ['type' => 'warning']
                ]);
                return $this->redirect(['controller' => 'Account', 'action' => 'myAccount']);
            }

            // Validate first name
            if (empty($data['name']) || strlen($data['name']) < 2) {

                $this->Flash->toast(__('First name must be at least 2 characters long.'), [
                    'element' => 'toast',
                    'params' => ['type' => 'warning']
                ]);
                return $this->redirect(['controller' => 'Account', 'action' => 'myAccount']);
            }

            // Validate mobile number
            if (!preg_match('/^\d{10,15}$/', $data['phone'])) {

                $this->Flash->toast(__('Invalid mobile number. It should be between 10 to 15 digits.'), [
                    'element' => 'toast',
                    'params' => ['type' => 'warning']
                ]);
                return $this->redirect(['controller' => 'Account', 'action' => 'myAccount']);
            }


            $profile_photo = $data['profile_photo'];
            $fileName = trim($profile_photo->getClientFilename());
            if (!empty($fileName)) {
                $imageTmpName = $profile_photo->getStream()->getMetadata('uri');
                $rand = strtoupper(substr(uniqid(sha1((string)time()), true), -5));
                $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                $folderPath = $uploadFolder;
                $targetdir = WWW_ROOT . $folderPath;
                $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                $uploadResult = $this->Media->upload($imageTmpName, $targetdir, $imageFile, $folderPath);

                $userId = $this->request->getSession()->read('Auth.User')['id'];
                $customer = $this->Customers->find('all')->where(['user_id' => $userId])->first();
                if ($customer) {
                    $customerData = [
                        'profile_photo' => $folderPath . $imageFile
                    ];
                    $customer = $this->Customers->patchEntity($customer, $customerData);
                    $this->Customers->save($customer);
                }
            }

            $userInfo['first_name'] = $data['name'];
            $userInfo['last_name'] = $data['last_name'];
            $userInfo['email'] = $data['email'];
            $userInfo['mobile_no'] = $data['phone'];
            $this->Users->patchEntity($user, $userInfo, [
                'fields' => ['first_name','last_name', 'email', 'mobile_no'] // Fields to update in Users
            ]);
            if ($this->Users->save($user)) {
                $customerData = [
                    'gender' => $data['gender'] ?? "M",
                    'date_of_birth' => $data['dob']
                ];
                $customer = $this->Customers->find('all')->where(['user_id' => $user->id])->first();
                if ($customer) {
                    $customer = $this->Customers->patchEntity($customer, $customerData);
                    if ($this->Customers->save($customer)) {
                        $this->Flash->toast(__("Your customer details have been updated."), [
                            'element' => 'toast',
                            'params' => ['type' => 'success']
                        ]);
                    } else {
                        $this->Flash->websiteError(__('Customer data could not be updated.'));
                    }
                } else {
                    $this->Flash->websiteError(__('Customer not found.'));
                }
            }
            return $this->redirect(['controller' => 'Account', 'action' => 'myAccount']);
        }
    }

    public function addWishlist()
    {

        $identity = $this->request->getSession()->read('Auth.User');

        if (!$identity) {
            $result = ['status' => __('error'), 'message' => __('You must be logged in to add items to your wishlist.')];
        } else {
            $users = $this->Users->find()
                ->contain([
                    'Customers' => function ($q) {
                        return $q->select(['id']); // Select only the Customer.id field
                    }
                ])
                ->select(['Users.id']) // Select the necessary fields from Users
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $identity->id])
                ->first();

            $customer_id = $users->customer->id;
            $data = $this->request->getData();
            $product_id = $data['product_id'];
            $variant_id = $data['variant_id'] ?? null;
            $atribute_id = $data['atribute_id'] ?? null;
              
            $result = $this->Wishlists->addToWishlist($customer_id, $product_id, $variant_id, $atribute_id);
            
            if (!$result) {
                $result = ['status' => __('error'), 'message' => __('Please try again!')];
            }
        }
        $this->response = $this->response->withType('application/json');

        return $this->response->withStringBody(json_encode($result));
    }

    public function removeWishlist()
    {

        $identity = $this->request->getSession()->read('Auth.User');

        if (!$identity) {
            $result = ['status' => __('error'), 'message' => __('User is not authenticated')];
        } else {
            $users = $this->Users->find()
                ->contain([
                    'Customers' => function ($q) {
                        return $q->select(['id']); // Select only the Customer.id field
                    }
                ])
                ->select(['Users.id']) // Select the necessary fields from Users
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $identity->id])
                ->first();

            $customer_id = $users->customer->id;
            $data = $this->request->getData();
            $product_id = $data['product_id'];
            $result = $this->Wishlists->removeFromWishlist($customer_id, $product_id);
            if ($result) {
                $result = ['status' => __('success'), 'message' => __('Item removed successfully.')];
            } else {
                $result = ['status' => __('error'), 'message' => __('Item already removed.')];
            }
        }
        $this->response = $this->response->withType('application/json');

        return $this->response->withStringBody(json_encode($result));
    }



    public function addToCart()
    {
        $this->response = $this->response->withType('application/json');
        if (!$this->request->is('post')) {
            $result = ['status' => __('error'), 'message' => __('Method Not Allowed')];
            return $this->response->withStringBody(json_encode($result));
        }


        $data = $this->request->getData();
        $guestToken = $this->request->getSession()->read('cartId') ?? null; // 7d1c54ba-05d8-4a5b-bad4-ed4381486858

        $identity = $this->request->getSession()->read('Auth.User');
        $result = [
            'status' => __('error'),
            'code' => 400,
            'data' => $data
        ];

        // Generate Guest Token if not logged in and no guest-token exists
        if (!$identity && !$guestToken) {
            $guestToken = Text::uuid();
            $this->request->getSession()->write('cartId', $guestToken);
        }

        $requiredFields = ['product_id', 'quantity'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $result = [
                    'status' => __('default', 'error'),
                    'code' => 200,
                    'message' => __('default', "Field '{0}' is required", $field)
                ];

                return $this->response
                    ->withType('application/json')
                    ->withStatus(200)
                    ->withStringBody(json_encode($result));
            }
        }


        // Determine Customer ID or Use Guest Token
        $customerId = $identity ? $identity->id : null;
        if (!empty($customerId)) {
            $users = $this->Users->find()
                ->contain(['Customers']) // Include related Customers
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $customerId])
                ->first();
            $customerId = $users->customer->id;
        }

        // Find or Create Cart
        $cartConditions = $customerId
            ? ['customer_id' => $customerId]
            : ['guest_token' => $guestToken];

        // echo "<pre>"; print_r($cartConditions); die;
        $cart = $this->Carts->find()->where($cartConditions)->first();
        if (!$cart) {
            $cart = $this->Carts->newEntity([
                'customer_id' => $customerId,
                'guest_token' => $guestToken,
            ]);
            if (!$this->Carts->save($cart)) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Failed to create cart')
                ];
                return $this->response->withStringBody(json_encode($result));
            }
        }

        // Add or Update Cart Item
        $connection = $this->CartItems->getConnection();
        $connection->begin();
        try {
           // Check if the item already exists in the cart
           $productVariantId = !empty($data['product_variant_id']) ? $data['product_variant_id'] : null;
           $productAttributeId = !empty($data['product_attribute_id']) ? $data['product_attribute_id'] : null;

           $query = $this->CartItems->find()->where([
               'cart_id' => $cart->id,
               'product_id' => $data['product_id'],
           ]);

           if ($productVariantId !== null) {
               $query->where(['product_variant_id' => $productVariantId]);
           } else {
               $query->where(['product_variant_id IS' => null]);
           }

           if ($productAttributeId !== null) {
               $query->where(['product_attribute_id' => $productAttributeId]);
           } else {
               $query->where(['product_attribute_id IS' => null]);
           }
           $existingItem = $query->first();

            if ($existingItem) {
                if($data['quantity'] == "NaN"){
                    $data['quantity'] = 1;
                }
                // Update quantity if the item exists
                $existingItem->quantity += $data['quantity'];
                if (!$this->CartItems->save($existingItem)) {
                    $result = [
                        'status' => __('error'),
                        'code' => 200,
                        'message' => __('Failed to update cart item')
                    ];
                    return $this->response->withStringBody(json_encode($result));
                }
            } else {
                if($data['quantity'] == "NaN"){
                    $data['quantity'] = 1;
                }
                // Add a new cart item
                $newItemData = [
                    'cart_id' => intval($cart->id),
                    'product_id' => intval($data['product_id']),
                    'quantity' => intval($data['quantity']),
                ];
                if (!empty($data['product_variant_id'])) {
                    $newItemData['product_variant_id'] = $data['product_variant_id'];
                }
                if (!empty($data['product_attribute_id'])) {
                    $newItemData['product_attribute_id'] = $data['product_attribute_id'];
                }

                $newItem = $this->CartItems->newEntity($newItemData);

                if (!$this->CartItems->save($newItem)) {
                    $result = [
                        'status' => __('error'),
                        'code' => 200,
                        'message' => __('Failed to add cart item.'),
                        'data' => $newItemData
                    ];
                    return $this->response->withStringBody(json_encode($result));
                }
            }

            $price = null;
            $name = '';
            if (isset($data['product_variant_id'])) {
                $productVariant = $this->CartItems->ProductVariants->find()
                    ->select(['promotion_price','variant_name'])
                    ->where(['id' => $data['product_variant_id']])
                    ->first();

                $price = $productVariant ? $productVariant->promotion_price : null;
                $name = $productVariant ? $productVariant->variant_name : null;
            }

            if (!$price) {
                $product = $this->CartItems->Products->find()
                    ->select(['promotion_price','name'])
                    ->where(['id' => $data['product_id']])
                    ->first();

                $price = $product ? $product->promotion_price : null;
                $name = $product ? $product->name : null;
            }

            $conditions = [
                'cart_id' => $cart->id,
                'product_id' => $data['product_id'],
            ];
            if (!empty($data['product_variant_id'])) {
                $conditions['product_variant_id'] = $data['product_variant_id'];
            }
            $updatedItem = $this->CartItems->find()
                ->where($conditions)
                ->first();

            if ($price !== null) {
                $updatedItem['price'] = $price;
                $updatedItem['total_price'] = number_format($updatedItem->quantity * $price, 2, '.', '');
            } else {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Price not found for the selected product/variant')
                ];
                return $this->response->withStringBody(json_encode($result));
            }

            // Fetch product category name and brand name
            // Fetch product details including category_id from ProductCategories if available
            $productDetails = $this->Products->find()
                ->select(['id', 'brand_id'])
                ->where(['id' => $data['product_id']])
                ->first();

            // Try to get category_id from ProductCategories table if exists
            $productCategory = $this->Products->ProductCategories
                ->find()
                ->select(['category_id'])
                ->where(['product_id' => $data['product_id']])
                ->first();

            if ($productCategory && !empty($productCategory->category_id)) {
                $productDetails->category_id = $productCategory->category_id;
            } else {
                $productDetails->category_id = null;
            }

            $categoryName = '';
            $brandName = '';

            if ($productDetails) {
                // Get category name
                if (!empty($productDetails->category_id)) {
                    $category = $this->Categories->find()
                        ->select(['name'])
                        ->where(['id' => $productDetails->category_id])
                        ->first();
                    $categoryName = $category ? $category->name : '';
                }
                // Get brand name
                if (!empty($productDetails->brand_id)) {
                    $brand = $this->Brands->find()
                        ->select(['name'])
                        ->where(['id' => $productDetails->brand_id])
                        ->first();
                    $brandName = $brand ? $brand->name : '';
                }
            }

            $connection->commit();

            $result = [
                'status' => __('success'),
                'code' => 200,
                'data' => $updatedItem,
                'name' => $name,
                'category_name' => $categoryName,
                'brand_name' => $brandName,
                'message' => __('Cart item updated successfully'),
                'guest_token' => $guestToken
            ];
            return $this->response->withStringBody(json_encode($result));

       } catch (\Exception $e) {
           $connection->rollback();

           $result = [
               'status' => __('error'),
               'code' => 200,
               'message' => __('Failed to update cart')
           ];
           return $this->response->withStringBody(json_encode($result));
       }
    }


    /****   Cart & Checkout   *****/
    public function cart()
    {

        $data = $this->request->getData();
        $guestToken = $this->request->getSession()->read('cartId') ?? null; // 7d1c54ba-05d8-4a5b-bad4-ed4381486858
        $totalSalePrice = 0;
        $customerAddress = '';
        $loyaltyDetails = "";
        $identity = $this->request->getSession()->read('Auth.User');
        // Generate Guest Token if not logged in and no guest-token exists
        if (!$identity && !$guestToken) {
            $guestToken = Text::uuid();
            $this->request->getSession()->write('cartId', $guestToken);
        }

        // Determine Customer ID or Use Guest Token
        $userId = null;
        $customerId = $identity ? $identity->id : null;
        if (!empty($customerId)) {
            $users = $this->Users->find()
                ->contain(['Customers']) // Include related Customers
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $customerId])
                ->first();
            $customerId = $users->customer->id;
            $customerAddress = $this->CustomerAddresses->listAddress($customerId);
            $userId = $identity->id;
        }

        if (isset($customerId) && !empty($customerId)) {
            $loyaltyDetails = $this->Loyalty->calculateLoyaltyPoints($customerId);
        }
        // Find or Create Cart
        $cartConditions = $customerId
            ? ['customer_id' => $customerId]
            : ['guest_token' => $guestToken];

        $cart = $this->Carts->find()
            ->where($cartConditions)
            ->contain(['CartItems' => ['Products']])  // ,'ProductVariants'
            ->first();

        $totalPrice = 0;
        $totalDiscountedPrice = 0;
        $cartItems = [];
        $cartOutItems = [];
        if ($cart) {
            foreach ($cart->cart_items as $item) {
                $price = null;
                $saleprice = null;
                if ($item->product_variant_id) {
                    $productVariant = $this->CartItems->ProductVariants->find()
                        ->select(['promotion_price', 'sales_price','variant_name'])
                        ->where(['id' => $item->product_variant_id])
                        ->first();

                    $item->product->name = $productVariant->variant_name;
                    $price = $productVariant ? $productVariant->promotion_price : null;
                    $saleprice = $productVariant ? $productVariant->sales_price : null;
                }else{
                    $price = $this->Products->getProductPrice($item->product_id);
                }
                if (!$price) {
                    $product = $this->CartItems->Products->find()
                        ->select(['promotion_price', 'sales_price'])
                        ->where(['id' => $item->product_id])
                        ->first();
                    $price = $product ? $product->promotion_price : null;
                    $saleprice = $product ? $product->sales_price : null;
                }
                $userId = $identity ? $identity->id : null;
                $whishListSatus = $userId === null ? false : $this->Wishlists->whishListCheckSingle($userId, $item->product_id);
                if ($price !== null) {
                    $image = $this->Products->getProductThumbnailImage($item->product_id, $item->product_variant_id);
                    $getAvailableStatus = $this->Products->singleProductStockStatus($item->product_id, $item->product_variant_id, $item->product_attribute_id);
                    if ($image) {
                        $image = $this->Media->getCloudFrontURL($image);
                    }
                    if ($getAvailableStatus == "In Stock") {
                        $salepriceWithCheckVariant = $saleprice ? $saleprice : $item->product->sales_price;
                        $totalPrice = $totalPrice + number_format($item->quantity * $price, 2, '.', '');
                        $totalSalePrice = $totalSalePrice + ($totalDiscountedPrice + number_format($item->quantity * $salepriceWithCheckVariant, 2, '.', ''));
                        $cartItems[] = [
                            'rating' => $this->Reviews->getAverageRating($item->product_id),
                            'total_review' => $this->Reviews->getTotalReviews($item->product_id),
                            'sale' => $saleprice ? $saleprice : $item->product->sales_price,
                            'discount' => $this->Products->getDiscountProduct($item->product_id, $item->product_variant_id),
                            'product_image' => $image,
                            'cart_item_id' => $item->id,
                            'whishlist' => $whishListSatus,
                            'reference_name' => $item->product->reference_name ?? "",
                            'url_key' => $item->product->url_key,
                            'product_id' => $item->product_id,
                            'product_variant_id' => $item->product_variant_id,
                            'product_name' => $item->product->name,
                            'variant_name' => $item->product_variant->name ?? $item->product->name,
                            'quantity' => $item->quantity,
                            'price' => $price,
                            'sale_price' => number_format($item->quantity * $saleprice, 2, '.', ''),
                            'get_available_status' => $getAvailableStatus,
                            'total_price' => number_format($item->quantity * $price, 2, '.', '')
                        ];
                    } else {
                        $cartOutItems[] = [
                            'rating' => $this->Reviews->getAverageRating($item->product_id),
                            'total_review' => $this->Reviews->getTotalReviews($item->product_id),
                            'sale' => $saleprice ? $saleprice : $item->product->sales_price,
                            'discount' => $this->Products->getDiscountProduct($item->product_id),
                            'product_image' => $image,
                            'cart_item_id' => $item->id,
                            'whishlist' => $whishListSatus,
                            'reference_name' => $item->product->reference_name ?? "",
                            'url_key' => $item->product->url_key,
                            'product_id' => $item->product_id,
                            'product_variant_id' => $item->product_variant_id,
                            'product_name' => $item->product->name,
                            'variant_name' => $item->product_variant->name ?? $item->product->name,
                            'quantity' => $item->quantity,
                            'price' => $price,
                            'sale_price' => number_format($item->quantity * $saleprice, 2, '.', ''),
                            'get_available_status' => $getAvailableStatus,
                            'total_price' => number_format($item->quantity * $price, 2, '.', '')
                        ];
                    }
                }
            }
        }
        $cart_id = $cart->id ?? 0;
        $total_items = count($cartItems);
        $totalDiscountedPrice = 0;
        $totalDiscountedPrice = $totalSalePrice - $totalPrice;
        $checkCartCount = $total_items + count($cartOutItems);
        $this->set(compact('userId','checkCartCount','customerAddress', 'customerId', 'loyaltyDetails', 'totalDiscountedPrice', 'totalPrice', 'cartOutItems', 'cartItems', 'cart_id', 'total_items'));
        $this->viewBuilder()->setTemplatePath('carts');
        $this->render('cart');
    }

    public function updateCartItem()
    {
        $this->response = $this->response->withType('application/json');

        // Validate HTTP Method
        if (!$this->request->is(['patch', 'post', 'put'])) {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            return $this->response->withStatus(405)->withStringBody(json_encode($result));
        }

        $data = $this->request->getData();
        $guestToken = $this->request->getSession()->read('cartId') ?? null;
        $identity = $this->request->getSession()->read('Auth.User');

        // Determine Customer ID or Use Guest Token
        $customerId = $identity ? $identity->id : null;
        if (!empty($customerId)) {
            $users = $this->Users->find()
                ->contain(['Customers']) // Include related Customers
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $customerId])
                ->first();
            $customerId = $users->customer->id;
        }

        if (!$customerId && !$guestToken) {
            $result = [
                'status' => __('error'),
                'code' => 400,
                'message' => __('Customer ID or Guest Token is required')
            ];
            return $this->response->withStatus(400)->withStringBody(json_encode($result));
        }

        // Validate Input Data
        if (empty($data['cart_item_id']) || empty($data['quantity'])) {
            $result = [
                'status' => __('error'),
                'code' => 400,
                'message' => __('Cart item ID and quantity are required')
            ];
            return $this->response->withStatus(400)->withStringBody(json_encode($result));
        }

        $cartConditions = $customerId
            ? ['customer_id' => $customerId]
            : ['guest_token' => $guestToken];

        // Find Cart
        $cart = $this->Carts->find()->where($cartConditions)->first();
        if (!$cart) {
            $result = [
                'status' => __('error'),
                'code' => 404,
                'message' => __('Cart not found')
            ];
            return $this->response->withStatus(404)->withStringBody(json_encode($result));
        }

        $connection = $this->CartItems->getConnection();
        $connection->begin();
        try {
            // Find Cart Item
            $cartItem = $this->CartItems->find()
                ->where([
                    'cart_id' => $cart->id,
                    'id' => $data['cart_item_id']
                ])
                ->first();

            if (!$cartItem) {
                $result = [
                    'status' => __('error'),
                    'code' => 404,
                    'message' => __('Cart item not found')
                ];
                return $this->response->withStatus(404)->withStringBody(json_encode($result));
            }

            // Update Quantity
            $cartItem->quantity = $data['quantity'];

            if (!$this->CartItems->save($cartItem)) {
                $result = [
                    'status' => __('error'),
                    'code' => 500,
                    'message' => __('Failed to update cart item')
                ];
                return $this->response->withStatus(500)->withStringBody(json_encode($result));
            }

            // Retrieve Price and Name
            $price = null;
            $name = '';
            if ($cartItem->product_variant_id) {
                $productVariant = $this->CartItems->ProductVariants->find()
                    ->select(['promotion_price', 'variant_name'])
                    ->where(['id' => $cartItem->product_variant_id])
                    ->first();

                $price = $productVariant ? $productVariant->promotion_price : null;
                $name = $productVariant ? $productVariant->variant_name : null;
            }

            if (!$price || !$name) {
                $product = $this->CartItems->Products->find()
                    ->select(['promotion_price', 'name'])
                    ->where(['id' => $cartItem->product_id])
                    ->first();

                $price = $product ? $product->promotion_price : null;
                $name = $product ? $product->name : null;
            }

            if ($price !== null) {
                $cartItem->price = $price;
                $cartItem->total_price = number_format($cartItem->quantity * $price, 2, '.', '');
            } else {
                $result = [
                    'status' => __('error'),
                    'code' => 400,
                    'message' => __('Price not found for the selected product/variant')
                ];
                return $this->response->withStatus(400)->withStringBody(json_encode($result));
            }

            // Fetch product category name and brand name
            $productDetails = $this->Products->find()
                ->select(['id', 'brand_id'])
                ->where(['id' => $cartItem->product_id])
                ->first();

            // Try to get category_id from ProductCategories table if exists
            $productCategory = $this->Products->ProductCategories
                ->find()
                ->select(['category_id'])
                ->where(['product_id' => $cartItem->product_id])
                ->first();

            if ($productCategory && !empty($productCategory->category_id)) {
                $productDetails->category_id = $productCategory->category_id;
            } else {
                $productDetails->category_id = null;
            }

            $categoryName = '';
            $brandName = '';

            if ($productDetails) {
                // Get category name
                if (!empty($productDetails->category_id)) {
                    $category = $this->Categories->find()
                        ->select(['name'])
                        ->where(['id' => $productDetails->category_id])
                        ->first();
                    $categoryName = $category ? $category->name : '';
                }
                // Get brand name
                if (!empty($productDetails->brand_id)) {
                    $brand = $this->Brands->find()
                        ->select(['name'])
                        ->where(['id' => $productDetails->brand_id])
                        ->first();
                    $brandName = $brand ? $brand->name : '';
                }
            }

            $connection->commit();

            $result = [
                'status' => __('success'),
                'code' => 200,
                'message' => __('Cart item updated successfully'),
                'data' => $cartItem,
                'name' => $name,
                'category_name' => $categoryName,
                'brand_name' => $brandName
            ];
            return $this->response->withStringBody(json_encode($result));
        } catch (\Exception $e) {
            $connection->rollback();
            $result = [
                'status' => __('error'),
                'code' => 500,
                'message' => __('Failed to update cart')
            ];
            return $this->response->withStatus(500)->withStringBody(json_encode($result));
        }
    }

    public function clearCart()
    {
        $this->response = $this->response->withType('application/json');

        // Validate HTTP Method
        if (!$this->request->is(['post'])) {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            return $this->response->withStatus(405)->withStringBody(json_encode($result));
        }

        $guestToken = $this->request->getSession()->read('cartId') ?? null;
        $identity = $this->request->getSession()->read('Auth.User');
        $customerId = null;

        if ($identity) {
            $users = $this->Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $identity->id])
                ->first();

            if ($users && !empty($users->customer)) {
                $customerId = $users->customer->id;
            }
        }

        $cartConditions = $customerId ? ['customer_id' => $customerId] : ['guest_token' => $guestToken];

        $cart = $this->Carts->find()
            ->where($cartConditions)
            ->first();

        if (!$cart) {
            $result = [
                'status' => __('success'),
                'code' => 200,
                'message' => __('Cart is already empty')
            ];
            return $this->response->withStatus(200)->withStringBody(json_encode($result));
        }

        // Delete all cart items
        $this->CartItems->deleteAll(['cart_id' => $cart->id]);

        $result = [
            'status' => __('success'),
            'code' => 200,
            'message' => __('Cart cleared successfully')
        ];
        return $this->response->withStatus(200)->withStringBody(json_encode($result));
    }

    public function deleteCartItem()
    {
        $this->response = $this->response->withType('application/json');

        // Validate HTTP Method
        if (!$this->request->is(['patch', 'post', 'put'])) {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            return $this->response->withStatus(405)->withStringBody(json_encode($result));
        }

        $data = $this->request->getData();
        $guestToken = $this->request->getSession()->read('cartId') ?? null;
        $identity = $this->request->getSession()->read('Auth.User');

        // Determine Customer ID or Use Guest Token
        $customerId = $identity ? $identity->id : null;
        if (!empty($customerId)) {
            $users = $this->Users->find()
                ->contain(['Customers']) // Include related Customers
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $customerId])
                ->first();
            $customerId = $users->customer->id;
        }

        if (!$customerId && !$guestToken) {
            $result = [
                'status' => __('error'),
                'code' => 400,
                'message' => __('Customer ID or Guest Token is required')
            ];
            return $this->response->withStatus(400)->withStringBody(json_encode($result));
        }

        // Validate cart item ID
        if (empty($data['cart_item_id'])) {
            $result = [
                'status' => __('error'),
                'code' => 400,
                'message' => __('Cart Item ID is required')
            ];
            return $this->response->withStatus(400)->withStringBody(json_encode($result));
        }

        $cartConditions = $customerId
            ? ['customer_id' => $customerId]
            : ['guest_token' => $guestToken];

        // Fetch the cart
        $cart = $this->Carts->find()->where($cartConditions)->first();
        if (!$cart) {
            $result = [
                'status' => __('error'),
                'code' => 400,
                'message' => __('Cart not found')
            ];
            return $this->response->withStatus(400)->withStringBody(json_encode($result));
        }

        // Fetch the cart item
        $cartItem = $this->CartItems->find()
            ->where([
                'cart_id' => $cart->id,
                'id' => $data['cart_item_id']
            ])
            ->first();

        if (!$cartItem) {
            $result = [
                'status' => __('error'),
                'code' => 400,
                'message' => __('Cart item not found or invalid cart item ID')
            ];
            return $this->response->withStatus(400)->withStringBody(json_encode($result));
        }

          // Get product information before deleting the cart item
        $productId = $cartItem->product_id;
        $productName = '';
        $price = $cartItem->price ?? 0;
        $quantity = $cartItem->quantity ?? 0;

        // Get product details directly from Products table to ensure we have all data
        $product = $this->Products->find()
            ->select(['id', 'name', 'brand_id','promotion_price'])
            ->where(['id' => $productId])
            ->first();
        $price = $product ? $product->promotion_price : $price;

        // Get product name
        if ($cartItem->product_variant_id) {
            $productVariant = $this->CartItems->ProductVariants->find()
                ->select(['variant_name','promotion_price'])
                ->where(['id' => $cartItem->product_variant_id])
                ->first();
            $productName = $productVariant ? $productVariant->variant_name : '';
            $price = $productVariant ? $productVariant->promotion_price : $price;
        }

        if (empty($productName) && $product) {
            $productName = $product->name;
        }

        // Get category name and brand name
        $categoryName = '';
        $brandName = '';

        // Try to get category_id from ProductCategories table
        $productCategory = $this->Products->ProductCategories
            ->find()
            ->select(['category_id'])
            ->where(['product_id' => $productId])
            ->first();

        if ($productCategory && !empty($productCategory->category_id)) {
            $category = $this->Categories->find()
                ->select(['name'])
                ->where(['id' => $productCategory->category_id])
                ->first();
            $categoryName = $category ? $category->name : '';
        }

        // Get brand name if product has brand_id
        if ($product && !empty($product->brand_id)) {
            $brand = $this->Brands->find()
                ->select(['name'])
                ->where(['id' => $product->brand_id])
                ->first();
            $brandName = $brand ? $brand->name : '';
        }

        // Store item information before deleting
        $itemInfo = [
            'product_id' => $productId,
            'product_name' => $productName,
            'price' => $price,
            'category_name' => $categoryName,
            'brand_name' => $brandName,
            'quantity' => $quantity
        ];

        // Attempt to delete the cart item
        if (!$this->CartItems->delete($cartItem)) {
            $result = [
                'status' => __('error'),
                'code' => 400,
                'message' => __('Failed to delete cart item')
            ];
            return $this->response->withStatus(400)->withStringBody(json_encode($result));
        }
        $result = [
            'status' => __('success'),
            'code' => 200,
            'data' => $itemInfo,
            'message' => __('Cart item deleted successfully')
        ];
        return $this->response->withStatus(200)->withStringBody(json_encode($result));
    }

    public function checkLoyaltyPoint()
    {
        $this->response = $this->response->withType('application/json');

        if (!$this->request->is('post')) {
            $result = ['status' => __('error'), 'message' => __('Method Not Allowed')];
            return $this->response->withStringBody(json_encode($result));
        }

        try {
            $identity = $this->request->getSession()->read('Auth.User');

            // Determine Customer ID or Use Guest Token
            $customerId = $identity ? $identity->id : null;

            if (!empty($customerId)) {
                $users = $this->Users->find()
                    ->contain(['Customers']) // Include related Customers
                    ->where(['Users.status' => 'A'])
                    ->where(['Users.id' => $customerId])
                    ->first();


                if (!$users || empty($users->customer)) {
                    throw new \Exception(__('Customer not found.'));
                }

                $customerId = $users->customer->id;
            }

            if (isset($customerId) && !empty($customerId)) {
                $loyaltyDetails = $this->Loyalty->calculateLoyaltyPoints($customerId);
            } else {
                throw new \Exception(__('Login rquired to check points.'));
            }
            if (empty($loyaltyDetails)) {
                throw new \Exception(__('Failed to retrieve loyalty details.'));
            }
            $result = $loyaltyDetails;
        } catch (\Exception $e) {
            $result = [
                'status' => __('error'),
                'code' => 500,
                'message' => $e->getMessage(),
                'data' => null,
            ];
        }

        return $this->response->withStringBody(json_encode($result));
    }

    public function loyaltyPointVerify()
    {
        $this->response = $this->response->withType('application/json');

        if (!$this->request->is('post')) {
            $result = ['status' => __('error'), 'message' => __('Method Not Allowed')];
            return $this->response->withStringBody(json_encode($result));
        }

        try {
            $identity = $this->request->getSession()->read('Auth.User');

            if (!$identity) {
            throw new \Exception(__('Login required to check points.'));
            }

            $customerId = $identity->id;
            $orderRedemption = (float)$this->request->getData('redeem_points');
            $finalAmount = (float)$this->request->getData('final_amount');

            $user = $this->Users->find()
            ->contain(['Customers'])
            ->where(['Users.status' => 'A', 'Users.id' => $customerId])
            ->first();

            if (!$user || empty($user->customer)) {
            throw new \Exception(__('Customer not found.'));
            }

            $customerId = $user->customer->id;
            $loyaltyDetails = $this->Loyalty->calculateLoyaltyPoints($customerId);

            if (empty($loyaltyDetails)) {
            throw new \Exception(__('Failed to retrieve loyalty details.'));
            }

            $availableCustomerPoints = (float)($loyaltyDetails['data']['points'] ?? 0);
          
            if ($orderRedemption <= 0) {
                return $this->response->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('You must redeem at least 1 point.'),
                ]));
            }

            // Check if redemption amount exceeds the available order amount
            // The user can't redeem more points than the order total value
            if ($orderRedemption > $finalAmount) {
                return $this->response->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('You cannot redeem more points ({0}) than the order total amount ({1}).', $orderRedemption, $finalAmount),
                    'data' => [
                        'available_points' => $availableCustomerPoints,
                        'max_redeemable' => $finalAmount,
                        'order_total' => $finalAmount
                    ]
                ]));
            }

            $loyaltySettings = $this->LoyaltySettings->getCustomGroupLoyaltyFirst($customerId);

            if (!$loyaltySettings || empty($loyaltySettings)) {
                return $this->response->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('Loyalty settings not found. Please contact to admin.'),
                ]));
            }

            // Check minimum threshold
            if ($finalAmount < $loyaltySettings['redeem_threshold_amount']) {
                return $this->response->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('The order total amount must exceed the minimum threshold of {0} to redeem loyalty points.', $loyaltySettings['redeem_threshold_amount']),
                ]));
            }

            // Convert currency amount to actual points based on customer group settings
            $redeemPointValue = (float)$loyaltySettings['redeem_point_value']; // Currency value per chunk
            $redeemPointsChunk = (float)$loyaltySettings['redeem_points'];     // Points per chunk

            // Calculate how many points are needed for the requested currency amount
            // Formula: points_needed = (currency_amount / point_value) * points_chunk
            $pointsNeeded = 0;
            if ($redeemPointValue > 0) {
                $pointsNeeded = ($orderRedemption / $redeemPointValue) * $redeemPointsChunk;
            }

            // Check if customer has enough points
            if ($pointsNeeded > $availableCustomerPoints) {
                return $this->response->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('Insufficient loyalty points. You need {0} points but only have {1} points.',
                        ceil($pointsNeeded), $availableCustomerPoints),
                    'data' => [
                        'points_needed' => ceil($pointsNeeded),
                        'available_points' => $availableCustomerPoints,
                        'currency_requested' => $orderRedemption
                    ]
                ]));
            }

            return $this->response->withStringBody(json_encode([
                'status' => 'success',
                'message' => __('Your redeemed points have been successfully applied: {0} FCFA', $orderRedemption),
                'data' => [
                    'currency_amount' => $orderRedemption,      // Currency amount to apply as discount
                    'points_to_deduct' => ceil($pointsNeeded),  // Actual points to deduct from customer
                    'available_points' => $availableCustomerPoints,
                    'order_total' => $finalAmount
                ]
            ]));

        } catch (\Exception $e) {
            $result = [
            'status' => 'error',
            'code' => 500,
            'message' => $e->getMessage(),
            ];
            return $this->response->withStringBody(json_encode($result));
        }

        return $this->response->withStringBody(json_encode($result));
    }


    public function applyofferCheck($coupon_code, $subTotal)
    {

        $this->response = $this->response->withType('application/json');
        try {
            $identity = $this->request->getSession()->read('Auth.User');
            $customerId = $identity ? $identity->id : null;

            if (!empty($customerId)) {
                $users = $this->Users->find()
                    ->contain(['Customers']) // Include related Customers
                    ->where(['Users.status' => 'A'])
                    ->where(['Users.id' => $customerId])
                    ->first();

                if (!$users || empty($users->customer)) {
                    throw new \Exception(__('Customer not found.'));
                }
                $customerId = $users->customer->id;
            }

            $couponCode = $coupon_code;
            $subTotal = $subTotal;

            $orderDate = date('Y-m-d H:i:s');

            $offerQuery = $this->Offers->find()
                ->where([
                    'offer_code' => $couponCode,
                    'status' => 'A',
                    'redeem_mode IN' => ['Online', 'Both'],
                    'offer_start_date <=' => $orderDate,
                    'AND' => [
                        'offer_end_date IS NOT' => null,
                        'offer_end_date >=' => $orderDate,
                        'OR' => [
                            ['min_cart_value IS' => null],
                            ['min_cart_value' => 0],
                            ['min_cart_value >' => 0, 'min_cart_value <=' => $subTotal]
                        ]
                    ]
                ]);
            $offer = $offerQuery->first();

            if (!$offer) {
                $result = [
                    'status' => __('error'),
                    'code' => 400,
                    'message' => __('No offer found matching the given coupon code.'),
                ];
                return $result;
            }

            $discountedAmount = $this->getDiscountAmount($subTotal, $offer->offer_type, $offer->discount, $offer->max_amt_per_disc_value);

            $result = [
                'status' => __('success'),
                'code' => 200,
                'message' => __('Coupon applied successfully'),
                'data' => ['coupon_amount' => $discountedAmount, 'offer_id' => $offer->id],
            ];
            return $result;

        } catch (\Exception $e) {
            // Catch any exceptions that occur during processing
            $result = [
                'status' => __('error'),
                'message' => $e->getMessage(), // Display the error message
            ];

            return $result;
        }

    }

    public function applyoffer()
    {
        if ($this->request->is('post')) {
            $this->response = $this->response->withType('application/json');
            try {
                $identity = $this->request->getSession()->read('Auth.User');


                $customerId = $identity ? $identity->id : null;

                if (!empty($customerId)) {
                    $users = $this->Users->find()
                        ->contain(['Customers']) // Include related Customers
                        ->where(['Users.status' => 'A'])
                        ->where(['Users.id' => $customerId])
                        ->first();

                    if (!$users || empty($users->customer)) {
                        throw new \Exception(__('Customer not found.'));
                    }
                    $customerId = $users->customer->id;
                }

                $couponCode = $this->request->getData('coupon_code');
                $subTotal = $this->request->getData('subTotal');

                $orderDate = date('Y-m-d H:i:s');

                $offerQuery = $this->Offers->find()
                    ->where([
                        'offer_code' => $couponCode,
                        'status' => 'A',
                        'redeem_mode IN' => ['Online', 'Both'],
                        'offer_start_date <=' => $orderDate,
                        'AND' => [
                            'offer_end_date IS NOT' => null,
                            'offer_end_date >=' => $orderDate,
                            'OR' => [
                                ['min_cart_value IS' => null],
                                ['min_cart_value' => 0],
                                ['min_cart_value >' => 0, 'min_cart_value <=' => $subTotal]
                            ]
                        ]
                    ]);
                $offer = $offerQuery->first();

                if (!$offer) {
                    $result = [
                        'status' => __('error'),
                        'code' => 200,
                        'message' => __('No offer found matching the given coupon code.'),
                    ];
                    return $this->response->withStringBody(json_encode($result));
                }


                $maxDiscount = isset($offer->max_amt_per_disc_value) && $offer->max_amt_per_disc_value !== '' ? $offer->max_amt_per_disc_value : null;
                $discountedAmount = $this->getDiscountAmount($subTotal, $offer->offer_type, $offer->discount, $maxDiscount);


             //   $discountedAmount = $this->getDiscountAmount($subTotal, $offer->offer_type, $offer->discount, $offer->max_amt_per_disc_value);

                $result = [
                    'status' => __('success'),
                    'code' => 200,
                    'message' => __('Coupon applied successfully'),
                    'data' => ['coupon_amount' => $discountedAmount],
                ];
                return $this->response->withStringBody(json_encode($result));

            } catch (\Exception $e) {
                // Catch any exceptions that occur during processing
                $result = [
                    'status' => __('error'),
                    'message' => $e->getMessage(), // Display the error message
                ];
                $this->response = $this->response->withStatus(200); // Bad Request status code
                return $this->response->withStringBody(json_encode($result));
            }
        }
    }


    public function getDiscountAmount($cartAmount, $offerType, $discountAmount, $maxDiscount = null)
    {
        // Check if the cart amount is greater than the given amount
        if ($cartAmount <= $discountAmount) {
            throw new \Exception("Cart amount must be greater than the given amount.");
        }

        // Check offer type: Percentage or Flat
        if ($offerType === 'Percentage') {
            // Check if max discount is provided and ensure the discount is within limits
        if ($maxDiscount !== null && $discountAmount > $maxDiscount) {
            throw new \Exception("The discount exceeds the maximum allowed discount.");
        }

            // Calculate the percentage discount based on the cart amount
            $calculatedDiscount = ($cartAmount * $discountAmount) / 100;

            // If there's a max discount, apply it
            if ($maxDiscount !== null && $calculatedDiscount > $maxDiscount) {
                $calculatedDiscount = $maxDiscount;
            }

            return $calculatedDiscount;

        } elseif ($offerType === 'Flat') {
            // For flat discount, just apply the given discount amount
            return $discountAmount;
        } else {
            throw new \Exception("Invalid offer type.");
        }
    }

    public function getCartShowRoomAddress()
    {
        $this->response = $this->response->withType('application/json');
        if (!$this->request->is(['post'])) {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            return $this->response->withStatus(405)->withStringBody(json_encode($result));
        }
        try {
            $search_str = $this->request->getData('search_str');
            $city_name = $this->request->getData('city_name');
            $data = $this->Showrooms->searchCartShowRoom($search_str, $city_name);

            $result = [
                'status' => __('success'),
                'code' => 200,
                'message' => __('Address item retrieved successfully'),
                'data' => $data
            ];

            // Return success response
            return $this->response->withStatus(200)->withStringBody(json_encode($result));
        } catch (\Exception $e) {
            $result = [
                'status' => __('error'),
                'code' => 500,
                'message' => __('An unexpected error occurred: ') . $e->getMessage()
            ];
            return $this->response->withStatus(500)->withStringBody(json_encode($result));
        }
    }

    /*****  Checkout Page Code start *******/
    public function checkout()
    {
        $ABIDJAN_CITY_ID = Configure::read('Constants.ABIDJAN_CITY_ID');
        $guestToken = $this->request->getSession()->read('cartId') ?? null;
        $totalSalePrice = 0;
        $customerAddress = '';
        $loyaltyDetails = "";
        $users = null;
        $identity = $this->request->getSession()->read('Auth.User');
        $customerId = $identity ? $identity->id : null;
        if (!empty($customerId)) {
            $users = $this->Users->find()
                ->contain(['Customers']) // Include related Customers
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $customerId])
                ->first();
            $customerId = $users->customer->id;
            $customerAddress = $this->CustomerAddresses->listAddress($customerId);
        }
        if (empty($identity)) {
            $this->Flash->toast(__('Login required for checkout.'), [
                'element' => 'toast',
                'params' => ['type' => 'warning']
            ]);
            return $this->redirect(['controller' => 'Account', 'action' => 'cart']);
        }
       
        $identity = $this->request->getSession()->read('Auth.User');
        // Generate Guest Token if not logged in and no guest-token exists
        if (!$identity && !$guestToken) {
            $guestToken = Text::uuid();
            $this->request->getSession()->write('cartId', $guestToken);
        }
        if (isset($customerId) && !empty($customerId)) {
            $loyaltyDetails = $this->Loyalty->calculateLoyaltyPoints($customerId);
        }
        // Find or Create Cart

        $savedCards = $this->CustomerCards->find()
            ->where(['customer_id' => $customerId])
            ->select(['id', 'card_holder_name', 'card_number', 'expiry_date', 'cvv'])
            ->toArray();


        $cartConditions = $customerId ? ['customer_id' => $customerId] : ['guest_token' => $guestToken];

        $cart = $this->Carts->find()
            ->where($cartConditions)
            ->contain(['CartItems' => ['Products']]) // , 'ProductVariants'  // Include cart items with product and variant data
            ->first();
        if (empty($cart->cart_items)) {
            return $this->redirect(['controller' => 'Account', 'action' => 'cart']);
        }
        $payByCreditActive = 0;
        $reg_id = $this->request->getQuery('reg_id');

        if ($reg_id) {
            $creditApplicationRecord = $this->CreditApplications->find()
            ->contain(['CartCreditPayments'])
            ->where(['registration_id' => $reg_id])
            ->first();
            if ($creditApplicationRecord) {
                if($creditApplicationRecord->cart_credit_payments[0]->customer_id == $customerId){
                    $payByCreditActive = true;
                    foreach($cart['cart_items'] as $key=>$val){
                        if($val->product_id != $creditApplicationRecord['cart_credit_payments'][0]['product_id']){
                            $cartItem = $this->CartItems->get($val->id);
                            $this->CartItems->delete($cartItem);
                        }
                        if($val->product_id == $creditApplicationRecord['cart_credit_payments'][0]['product_id'])
                        {
                            $cartItem = $this->CartItems->get($val->id);
                            $cartItem->quantity = 1;
                            $this->CartItems->save($cartItem);
                        }
                    }
                }
                else{
                    $this->Flash->websiteError(__('You are not authorized to view this application.'));
                    return $this->redirect(['controller' => 'Account', 'action' => 'checkout']);
                }
            }
        }
        $totalPrice = 0;
        $totalDiscountedPrice = 0;
        $cartItems = [];
        $cartOutItems = [];
        $weightQuantityArray = [];
        $sizeQuantityArray = [];
        if ($cart) {
            foreach ($cart->cart_items as $k => $item) {

                $brand = '';
                $category_name = '';
                $productInfo = $this->CartItems->Products->find()
                ->select(['id', 'promotion_price', 'sales_price', 'brand_id', 'Brands.name'])
                ->contain(['Brands' => function($q) {
                    return $q->select(['name']);
                }])
                ->contain(['ProductCategories' => function($q) {
                    return $q->select(['product_id', 'category_id'])
                        ->contain(['Categories' => function($q) {
                            return $q->select(['id', 'name']);
                        }]);
                }])
                ->where(['Products.id' => $item->product_id])
                ->first();
                
                $brand = $productInfo->brand->name ?? '';
                $category_name = $productInfo->product_categories[0]->category->name ?? '';

                $price = null;
                $saleprice = null;
                if ($item->product_variant_id) {
                    $productVariant = $this->CartItems->ProductVariants->find()
                        ->select(['promotion_price', 'sales_price','variant_name'])
                        ->where(['id' => $item->product_variant_id])
                        ->first();
                    $item->product->name = $productVariant->variant_name;
                    $price = $productVariant ? $productVariant->promotion_price : null;
                    $saleprice = $productVariant ? $productVariant->sales_price : null;
                }else{
                    $price = $this->Products->getProductPrice($item->product_id);
                }
                if (!$price) {
                    $product = $this->CartItems->Products->find()
                        ->select(['promotion_price', 'sales_price'])
                        ->where(['id' => $item->product_id])
                        ->first();
                    $price = $product ? $product->promotion_price : null;
                    $saleprice = $product ? $product->sales_price : null;
                }
                if ($price !== null) {
                    $image = $this->Products->getProductThumbnailImage($item->product_id, $item->product_variant_id);
                    $getAvailableStatus = $this->Products->singleProductStockStatus($item->product_id, $item->product_variant_id, $item->product_attribute_id);
                    if ($image) {
                        $image = $this->Media->getCloudFrontURL($image);
                    }
                    $userId = $identity ? $identity->id : null;
                    $whishListSatus = $userId === null ? false : $this->Wishlists->whishListCheckSingle($userId, $item->product_id);
                    if ($getAvailableStatus == "In Stock") {
                        $salepriceWithCheckVariant = $saleprice ? $saleprice : $item->product->sales_price;
                        $weightQuantityArray[] = ['weight' => $item->product->product_weight, 'quantity' => $item->quantity];
                        $sizeQuantityArray[] = ['size' => $item->product->product_size, 'quantity' => $item->quantity];
                        $totalPrice = $totalPrice + number_format($item->quantity * $price, 2, '.', '');
                        $totalSalePrice = $totalSalePrice + ($totalDiscountedPrice + number_format($item->quantity * $salepriceWithCheckVariant, 2, '.', ''));
                        $cartItems[] = [
                            'category_name' => $category_name ?? "",
                            'url_key' => $item->product->url_key ?? "",
                            'avl_on_credit' => $item->product->avl_on_credit ?? '',
                            'brand_name' => $brand,
                            'discount' => $this->Products->getDiscountProduct($item->product_id, $item->product_variant_id),
                            'sale' => $saleprice ? $saleprice : $item->product->sales_price,
                            'product_image' => $image,
                            'cart_item_id' => $item->id,
                            'whishlist' => $whishListSatus,
                            'product_id' => $item->product_id,
                            'reference_name' => $item->product->reference_name ?? "",
                            'product_variant_id' => $item->product_variant_id,
                            'product_name' => $item->product->name,
                            'variant_name' => $item->product_variant->name ?? "",
                            'quantity' => $item->quantity,
                            'price' => $price,
                            'sale_price' => number_format($item->quantity * $saleprice, 2, '.', ''),
                            'get_available_status' => $getAvailableStatus,
                            'total_price' => number_format($item->quantity * $price, 2, '.', '')
                        ];
                    } else {
                        $cartOutItems[] = [
                            'category_name' => $category_name ?? "",
                            'url_key' => $item->product->url_key ?? "",
                            'brand_name' => $brand,
                            'avl_on_credit' => $item->product->avl_on_credit ?? '',
                            'sale' => $saleprice ? $saleprice : $item->product->sales_price,
                            'discount' => $this->Products->getDiscountProduct($item->product_id),
                            'product_image' => $image,
                            'cart_item_id' => $item->id,
                            'whishlist' => $whishListSatus,
                            'product_id' => $item->product_id,
                            'product_variant_id' => $item->product_variant_id,
                            'reference_name' => $item->product->reference_name ?? "",
                            'product_name' => $item->product->name,
                            'variant_name' => $item->product_variant && $item->product_variant->name
                                ? $item->product_variant->name
                                : $item->product->name,
                            'quantity' => $item->quantity,
                            'price' => $price,
                            'sale_price' => number_format($item->quantity * $saleprice, 2, '.', ''),
                            'get_available_status' => $getAvailableStatus,
                            'total_price' => number_format($item->quantity * $price, 2, '.', '')
                        ];
                    }
                }
            }
        }
        $cart_id = $cart->id ?? 0;
        $total_items = count($cartItems);
        $totalDiscountedPrice = 0;
               $totalDiscountedPrice = $totalSalePrice - $totalPrice;
        $methods = $this->PaymentMethods->listMethods();
        $mobile = $users->mobile_no;
        if (empty($mobile)) {
            $mobile = '';
        }
        $mtn_country_code = $users->country_code;
        // Get cities for the address form
        $cities = $this->fetchTable('Cities')->listCity();
       // dd($cartItems);
        $showrooms = $this->Showrooms->searchCartShowRoom('');

        $wallet = $this->Wallets->getMyWalletAmount($customerId);
        if ($wallet && isset($wallet->balance)) {
            $wallet = $wallet->balance;
        } else {
            $wallet = 0;
        }





        $creditPartnersList = $this->Partners->getAllPartners();
        //dd($creditPartnersList);

       
        $this->set(compact('ABIDJAN_CITY_ID','users','creditPartnersList','wallet','mtn_country_code','showrooms','payByCreditActive','weightQuantityArray','sizeQuantityArray', 'mobile', 'methods', 'savedCards', 'customerAddress', 'customerId', 'loyaltyDetails', 'totalDiscountedPrice', 'totalPrice', 'cartOutItems', 'cartItems', 'cart_id', 'total_items', 'cities'));
        $this->viewBuilder()->setTemplatePath('carts');
        $this->render('checkout');
    }

    public function addNewCardDetails()
    {
        $this->response = $this->response->withType('application/json');
        if (!$this->request->is(['post'])) {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            return $this->response->withStatus(405)->withStringBody(json_encode($result));
        }
        try {
            $identity = $this->request->getSession()->read('Auth.User');
            $customerId = $identity ? $identity->id : null;
            if (!empty($customerId)) {
                $users = $this->Users->find()
                    ->contain(['Customers']) // Include related Customers
                    ->where(['Users.status' => 'A'])
                    ->where(['Users.id' => $customerId])
                    ->first();

                if (!$users || empty($users->customer)) {
                    throw new \Exception(__('Customer not found.'));
                }
                $customerId = $users->customer->id;
            }
            $data['customer_id'] = $customerId;
            $data['card_holder_name'] = $this->request->getData('card_holder_name');
            $data['card_number'] = $this->request->getData('card_number');
            $data['cvv'] = $this->request->getData('cvv');
            $data['expiry_date'] = $this->request->getData('expiry_date');
            $cardId = $this->CustomerCards->add_record($data);

            if ($cardId) {
                $result = [
                    'status' => 'success',
                    'code' => 201,
                    'data' => ['id' => $cardId],
                    'message' => __('Card added successfully.')
                ];
            } else {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Failed to add card. Please try again.')
                ];
            }
            // Return success response
            return $this->response->withStatus(200)->withStringBody(json_encode($result));
        } catch (\Exception $e) {
            $result = [
                'status' => __('error'),
                'code' => 500,
                'message' => __('An unexpected error occurred: ') . $e->getMessage()
            ];
            return $this->response->withStatus(500)->withStringBody(json_encode($result));
        }
    }

    public function editNewCardDetails()
    {
        $this->response = $this->response->withType('application/json');
        if (!$this->request->is(['post'])) {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            return $this->response->withStatus(405)->withStringBody(json_encode($result));
        }
        try {
            $identity = $this->request->getSession()->read('Auth.User');
            $customerId = $identity ? $identity->id : null;
            if (!empty($customerId)) {
                $users = $this->Users->find()
                    ->contain(['Customers']) // Include related Customers
                    ->where(['Users.status' => 'A'])
                    ->where(['Users.id' => $customerId])
                    ->first();

                if (!$users || empty($users->customer)) {
                    throw new \Exception(__('Customer not found.'));
                }
                $customerId = $users->customer->id;
            }
            $data['customer_id'] = $customerId;
            $data['card_holder_name'] = $this->request->getData('card_holder_name');
            $data['card_number'] = $this->request->getData('card_number');
            $data['cvv'] = $this->request->getData('cvv');
            $data['expiry_date'] = $this->request->getData('expiry_date');
            $cardId = $this->CustomerCards->update_record($this->request->getData('id'), $data);

            if ($cardId) {
                $result = [
                    'status' => 'success',
                    'code' => 201,
                    'data' => ['id' => $cardId],
                    'message' => __('Card updated successfully.')
                ];
            } else {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Failed to add card. Please try again.')
                ];
            }
            // Return success response
            return $this->response->withStatus(200)->withStringBody(json_encode($result));
        } catch (\Exception $e) {
            $result = [
                'status' => __('error'),
                'code' => 500,
                'message' => __('An unexpected error occurred: ') . $e->getMessage()
            ];
            return $this->response->withStatus(500)->withStringBody(json_encode($result));
        }
    }

    public function deleteCard()
    {
        $this->response = $this->response->withType('application/json');
        if (!$this->request->is(['post'])) {
            $result = [
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ];
            return $this->response->withStatus(405)->withStringBody(json_encode($result));
        }
        try {
            $identity = $this->request->getSession()->read('Auth.User');
            $customerId = $identity ? $identity->id : null;
            if (!empty($customerId)) {
                $users = $this->Users->find()
                    ->contain(['Customers']) // Include related Customers
                    ->where(['Users.status' => 'A'])
                    ->where(['Users.id' => $customerId])
                    ->first();

                if (!$users || empty($users->customer)) {
                    throw new \Exception(__('Customer not found.'));
                }
                $customerId = $users->customer->id;
            }
            $savedCard = $this->CustomerCards->get($this->request->getData('id'));

            if ($savedCard->customer_id !== $customerId) {
                $result = [
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Unauthorized to delete this card.')
                ];
                $this->response = $this->response->withStatus(200);
            } else {
                if ($this->CustomerCards->delete($savedCard)) {
                    $result = [
                        'status' => 'success',
                        'code' => 200,
                        'message' => __('Card deleted successfully.')
                    ];
                    $this->response = $this->response->withStatus(200);
                } else {
                    $result = [
                        'status' => 'error',
                        'code' => 200,
                        'message' => __('Failed to delete card. Please try again.')
                    ];
                    $this->response = $this->response->withStatus(200);
                }
            }

            // Return success response
            return $this->response->withStatus(200)->withStringBody(json_encode($result));
        } catch (\Exception $e) {
            $result = [
                'status' => __('error'),
                'code' => 500,
                'message' => __('An unexpected error occurred: ') . $e->getMessage()
            ];
            return $this->response->withStatus(500)->withStringBody(json_encode($result));
        }
    }

    private function checkPayByCreditItem($productIds = [])
    {
        // Check if all products in $productIds have avl_on_credit = 1
        if (empty($productIds)) {
            return [
                    'status' => false,
                    'message' => __('Cart is empty or no products found.')
                ];
        }

        $products = $this->Products->find()
            ->select(['id', 'avl_on_credit'])
            ->where(['id IN' => $productIds])
            ->all();

        foreach ($products as $product) {
            if ((int)$product->avl_on_credit != 1) {
                return [
                    'status' =>  __('error'),
                    'message' => __('Please remove "Not Available On Credit" products from cart to proceed with Credit Payment.')
                ];
            }
        }

        return ['status' =>  __('success'), 'message' => __('All products are available for credit payment.')];
    }

    /**
     * Create a new order with comprehensive validation and payment processing
     *
     * @return \Cake\Http\Response JSON response with order creation status
     */
    public function createOrder()
    {
        try {
            // Set response type early
            $this->response = $this->response->withType('application/json');

            // Validate request method
            if (!$this->request->is('post')) {
                return $this->_errorResponse(405, __('Method not allowed'));
            }

            // Get and validate request data
            $data = $this->request->getData();
            if (empty($data)) {
                return $this->_errorResponse(400, __('Invalid request data'));
            }

            // Validate and get customer information
            $customerData = $this->_validateAndGetCustomer();
            if (isset($customerData['error'])) {
                return $customerData['error'];
            }

            $customerId = $customerData['customer_id'];
            $roleID = $customerData['role_id'];

            // Validate delivery information
            $deliveryValidation = $this->_validateDeliveryInfo($data);
            if (isset($deliveryValidation['error'])) {
                return $deliveryValidation['error'];
            }

            // Validate credit payment eligibility if applicable
            $creditValidation = $this->_validateCreditPayment($data);
            if (isset($creditValidation['error'])) {
                return $creditValidation['error'];
            }

            // Calculate order totals and validate cart
            $orderCalculation = $this->_calculateOrderTotals($customerId, $data);
            if (isset($orderCalculation['error'])) {
                return $orderCalculation['error'];
            }

            // Validate payment amounts
            $paymentValidation = $this->_validatePaymentAmounts($customerId, $data, $orderCalculation);
            if (isset($paymentValidation['error'])) {
                return $paymentValidation['error'];
            }

            // Process payment gateway
            $paymentResult = $this->_processPaymentGateway($data, $paymentValidation);
            if (isset($paymentResult['error'])) {
                return $paymentResult['error'];
            }

            // Create order if payment processing succeeded
            if ($paymentResult['create_order']) {
                $orderResult = $this->_createOrderRecord($data, $customerId, $roleID, $orderCalculation, $paymentValidation);
                return $orderResult;
            }

            return $this->_errorResponse(500, __('Order creation failed'));

        } catch (\Exception $e) {
            $this->log('Order creation error: ' . $e->getMessage(), 'error');
            return $this->_errorResponse(500, __('An unexpected error occurred. Please try again.'));
        }
    }

    /**
     * Validate and get customer information
     *
     * @return array Customer data or error response
     */
    private function _validateAndGetCustomer()
    {
        $identity = $this->request->getSession()->read('Auth.User');
        $customerId = $identity ? $identity->id : null;

        if (!$customerId) {
            return ['error' => $this->_errorResponse(401, __('User login required to place order'))];
        }

        $user = $this->Users->find()
            ->contain(['Customers'])
            ->where(['Users.status' => 'A', 'Users.id' => $customerId])
            ->first();

        if (!$user || empty($user->customer)) {
            return ['error' => $this->_errorResponse(404, __('Customer not found'))];
        }

        return [
            'customer_id' => $user->customer->id,
            'role_id' => $user->role_id ?? 3
        ];
    }

    /**
     * Validate delivery information
     *
     * @param array $data Request data
     * @return array Success or error response
     */
    private function _validateDeliveryInfo($data)
    {
        if (empty($data['showroom_id']) && empty($data['customer_address_id'])) {
            return ['error' => $this->_errorResponse(400, __('Address required for checkout. Please provide either a showroom or a customer address.'))];
        }

        return ['success' => true];
    }

    /**
     * Validate credit payment eligibility
     *
     * @param array $data Request data
     * @return array Success or error response
     */
    private function _validateCreditPayment($data)
    {
        if (!isset($data['payment_method']) || $data['payment_method'] !== 'Credit') {
            return ['success' => true];
        }

        if (!isset($data['cart_items']) || !is_array($data['cart_items'])) {
            return ['error' => $this->_errorResponse(400, __('Invalid cart items for credit payment'))];
        }

        $productIds = array_filter(
            array_map(fn($item) => isset($item['product_id']) ? (int)$item['product_id'] : null, $data['cart_items']),
            fn($id) => $id !== null
        );

        if (empty($productIds)) {
            return ['error' => $this->_errorResponse(400, __('No valid products found for credit payment'))];
        }

        $checkCreditPayment = $this->checkPayByCreditItem($productIds);
        if ($checkCreditPayment['status'] !== __('success')) {
            return ['error' => $this->_errorResponse(400, $checkCreditPayment['message'])];
        }

        return ['success' => true];
    }

    /**
     * Calculate order totals and validate cart
     *
     * @param int $customerId Customer ID
     * @param array $data Request data
     * @return array Order calculation data or error response
     */
    private function _calculateOrderTotals($customerId, $data)
    {
        // Get cart data
        $guestToken = $this->request->getSession()->read('cartId') ?? null;
        $cartConditions = $customerId ? ['customer_id' => $customerId] : ['guest_token' => $guestToken];

        $cart = $this->Carts->find()
            ->where($cartConditions)
            ->contain(['CartItems' => ['Products']])
            ->first();

        if (!$cart || empty($cart->cart_items)) {
            return ['error' => $this->_errorResponse(400, __('Cart is empty'))];
        }

        $totalPrice = 0;
        $weightQuantityArray = [];
        $sizeQuantityArray = [];
        $orderItemsData = [];
        $hasNonCreditItems = false;

        foreach ($cart->cart_items as $item) {
            $price = $this->_getItemPrice($item);
            if ($price === null) {
                continue;
            }

            $stockStatus = $this->Products->singleProductStockStatus(
                $item->product_id,
                $item->product_variant_id,
                $item->product_attribute_id
            );

            if ($stockStatus !== "In Stock") {
                return ['error' => $this->_errorResponse(400, __('Some items in your cart are out of stock'))];
            }

            // Check credit availability for credit payments
            if ($data['payment_method'] === 'Credit' && $item->product->avl_on_credit != 1) {
                $hasNonCreditItems = true;
            }

            $weightQuantityArray[] = ['weight' => $item->product->product_weight, 'quantity' => $item->quantity];
            $sizeQuantityArray[] = ['size' => $item->product->product_size, 'quantity' => $item->quantity];

            $itemTotal = number_format($item->quantity * $price, 2, '.', '');
            $totalPrice += $itemTotal;

            $orderItem = [
                'product_id' => $item->product_id,
                'quantity' => $item->quantity,
                'price' => $price,
                'total_price' => $itemTotal,
                'status' => 'Pending',
            ];

            if (!empty($item->product_variant_id)) {
                $orderItem['product_variant_id'] = $item->product_variant_id;
            }

            if (!empty($item->product_attribute_id)) {
                $orderItem['product_attribute_id'] = $item->product_attribute_id;
            }

            $orderItemsData[] = $orderItem;
        }

        if ($data['payment_method'] === 'Credit' && $hasNonCreditItems) {
            return ['error' => $this->_errorResponse(400, __('Credit option is not available for some products in your cart'))];
        }

        // Validate loyalty points
        $loyaltyValidation = $this->_validateLoyaltyPoints($customerId, $data);
        if (isset($loyaltyValidation['error'])) {
            return $loyaltyValidation;
        }

        // Validate and apply coupon
        $couponData = $this->_validateAndApplyCoupon($data, $totalPrice);

        // Calculate delivery charges
        $deliveryData = $this->_calculateDeliveryCharges($data, $weightQuantityArray, $sizeQuantityArray);
        if (isset($deliveryData['error'])) {
            return $deliveryData;
        }

        return [
            'total_price' => $totalPrice,
            'order_items' => $orderItemsData,
            'weight_quantity' => $weightQuantityArray,
            'size_quantity' => $sizeQuantityArray,
            'coupon_amount' => $couponData['amount'],
            'offer_id' => $couponData['offer_id'],
            'delivery_charge' => $deliveryData['charge'],
            'city_id' => $deliveryData['city_id']
        ];
    }

    /**
     * Get item price from product or variant
     *
     * @param object $item Cart item
     * @return float|null Item price
     */
    private function _getItemPrice($item)
    {
        if ($item->product_variant_id) {
            $productVariant = $this->CartItems->ProductVariants->find()
                ->select(['promotion_price', 'sales_price'])
                ->where(['id' => $item->product_variant_id])
                ->first();

            if ($productVariant) {
                return $productVariant->promotion_price;
            }
        }

        // Try product price method
        $price = $this->Products->getProductPrice($item->product_id);
        if ($price) {
            return $price;
        }

        // Fallback to direct product query
        $product = $this->CartItems->Products->find()
            ->select(['promotion_price', 'sales_price'])
            ->where(['id' => $item->product_id])
            ->first();

        return $product ? $product->promotion_price : null;
    }

    /**
     * Validate loyalty points
     *
     * @param int $customerId Customer ID
     * @param array $data Request data
     * @return array Success or error response
     */
    private function _validateLoyaltyPoints($customerId, $data)
    {
        if (empty($data['redeem_points']) || $data['redeem_points'] <= 0) {
            return ['success' => true];
        }

        $loyaltyDetails = $this->Loyalty->calculateLoyaltyPoints($customerId);
        $availablePoints = $loyaltyDetails['data']['points_converted'] ?? 0;

        if ($availablePoints < $data['redeem_points']) {
            return ['error' => $this->_errorResponse(400, __('Insufficient loyalty points or points expired'))];
        }

        return ['success' => true];
    }

    /**
     * Validate and apply coupon
     *
     * @param array $data Request data
     * @param float $totalPrice Order total
     * @return array Coupon data
     */
    private function _validateAndApplyCoupon($data, $totalPrice)
    {
        if (empty($data['coupon_code'])) {
            return ['amount' => 0, 'offer_id' => null];
        }

        $couponResult = $this->applyofferCheck($data['coupon_code'], $totalPrice);

        return [
            'amount' => $couponResult['data']['coupon_amount'] ?? 0,
            'offer_id' => $couponResult['data']['offer_id'] ?? null
        ];
    }

    /**
     * Calculate delivery charges
     *
     * @param array $data Request data
     * @param array $weightQuantityArray Weight data
     * @param array $sizeQuantityArray Size data
     * @return array Delivery data or error response
     */
    private function _calculateDeliveryCharges($data, $weightQuantityArray, $sizeQuantityArray)
    {
        $cityId = null;

        if (!empty($data['customer_address_id'])) {
            $address = $this->CustomerAddresses->find()
                ->select(['city_id'])
                ->where(['id' => $data['customer_address_id']])
                ->first();
            $cityId = $address ? $address->city_id : null;
        }

        if (!$cityId) {
            return ['charge' => 0, 'city_id' => null];
        }

        $deliveryCharges = $this->getDeliveryChargeFromCityAPINew(
            $cityId,
            $data['delivery_mode_type'],
            $weightQuantityArray,
            $sizeQuantityArray
        );

        return [
            'charge' => $deliveryCharges['total_delivery_charge'] ?? 0,
            'city_id' => $cityId
        ];
    }

    /**
     * Validate payment amounts and wallet usage
     *
     * @param int $customerId Customer ID
     * @param array $data Request data
     * @param array $orderCalculation Order calculation data
     * @return array Payment validation data or error response
     */
    private function _validatePaymentAmounts($customerId, $data, $orderCalculation)
    {
        $totalPrice = $orderCalculation['total_price'];
        $couponAmount = $orderCalculation['coupon_amount'];
        $deliveryCharge = $orderCalculation['delivery_charge'];

        // Calculate final amount before wallet
        $finalAmount = $totalPrice - ($couponAmount + ($data['inputPointVal'] ?? 0)) + $deliveryCharge;

        // Handle wallet payment
        $walletData = $this->_processWalletPayment($customerId, $data, $finalAmount);
        if (isset($walletData['error'])) {
            return $walletData;
        }

        // Validate final amount matches frontend calculation
        if (abs($data['final_amount'] - $walletData['final_amount']) > 0.01) {
            return ['error' => $this->_errorResponse(400, __('Payment amount mismatch. Please refresh and try again.'))];
        }

        return $walletData;
    }

    /**
     * Process wallet payment logic
     *
     * @param int $customerId Customer ID
     * @param array $data Request data
     * @param float $finalAmount Final order amount
     * @return array Wallet processing data or error response
     */
    private function _processWalletPayment($customerId, $data, $finalAmount)
    {
        $walletBalance = $this->Wallets->getMyWalletAmount($customerId);
        $availableWallet = $walletBalance->balance ?? 0;
        $requestedWallet = $data['wallet'] ?? 0;

        if ($requestedWallet > $availableWallet) {
            return ['error' => $this->_errorResponse(400, __('Insufficient wallet balance'))];
        }

        $walletToUse = min($requestedWallet, $availableWallet, $finalAmount);
        $finalAmountAfterWallet = max(0, $finalAmount - $walletToUse);

        // Determine if payment gateway is needed
        $paymentGatewayRequired = $finalAmountAfterWallet > 0;

        // Override payment method if fully covered by wallet
        $paymentMethod = $data['payment_method'];
        if (!$paymentGatewayRequired && $walletToUse > 0) {
            $paymentMethod = 'Waiver';
        }

        return [
            'final_amount' => $finalAmountAfterWallet,
            'wallet_amount' => $walletToUse,
            'payment_method' => $paymentMethod,
            'payment_gateway_required' => $paymentGatewayRequired
        ];
    }

    /**
     * Process payment gateway
     *
     * @param array $data Request data
     * @param array $paymentValidation Payment validation data
     * @return array Payment processing result
     */
    private function _processPaymentGateway($data, $paymentValidation)
    {
        $paymentMethod = $paymentValidation['payment_method'];
        $finalAmount = $paymentValidation['final_amount'];

        // If no payment gateway required (wallet covers full amount or zero amount)
        if (!$paymentValidation['payment_gateway_required']) {
            return [
                'create_order' => true,
                'message' => __('Your order has been successfully created.'),
                'payment_status' => 'SUCCESSFUL'
            ];
        }

        $orderNumber = $this->Orders->generateUniqueOrderNum();
        $mobileNo = $data['mobile_no'] ?? null;

        switch ($paymentMethod) {
            case 'MTN MoMo':
                return $this->_processMtnMomoPayment($orderNumber, $finalAmount, $mobileNo);

            case 'Wave':
                return $this->_processWavePayment($orderNumber, $finalAmount);

            case 'Cash on Delivery':
                return $this->_processCodPayment($data);

            case 'Credit':
            case 'Waiver':
                return [
                    'create_order' => true,
                    'message' => __('Your order has been successfully created.'),
                    'payment_status' => $paymentMethod === 'Waiver' ? 'paid' : 'pending'
                ];

            default:
                return ['error' => $this->_errorResponse(400, __('Invalid payment method'))];
        }
    }
    /**
     * Process MTN MoMo payment
     *
     * @param string $orderNumber Order number
     * @param float $amount Payment amount
     * @param string $mobileNo Mobile number
     * @return array Payment result
     */
    private function _processMtnMomoPayment($orderNumber, $amount, $mobileNo)
    {
        $pgResult = $this->Mtn->initiatePayment($orderNumber, $amount, $mobileNo);

        if (!$pgResult || !is_array($pgResult)) {
            return ['error' => $this->_errorResponse(500, __('Failed to initiate MTN payment'))];
        }

        // Remove debug info if present
        unset($pgResult['debug_info']);

        $apiStatus = $pgResult['status'] ?? 'error';
        $httpCode = $pgResult['httpcode'] ?? 500;

        if ($apiStatus === 'error') {
            $message = $httpCode === 200 ? $pgResult['reason'] : $pgResult['message'];
            return ['error' => $this->_errorResponse($httpCode, $message)];
        }

        $paymentStatus = $pgResult['payment_status'];
        $message = $paymentStatus === 'SUCCESSFUL'
            ? __('Payment Successful! Thank you for your payment; your order has been successfully created.')
            : __('Payment is pending! Your order has been successfully created.');

        return [
            'create_order' => true,
            'message' => $message,
            'payment_status' => $paymentStatus
        ];
    }

    /**
     * Process Wave payment
     *
     * @param string $orderNumber Order number
     * @param float $amount Payment amount
     * @return array Payment result
     */
    private function _processWavePayment($orderNumber, $amount)
    {
        $pgResult = $this->Wave->createCheckoutSession($orderNumber, $amount);
        $apiStatus = $pgResult['status'];
        $httpCode = $pgResult['httpcode'];

        if ($apiStatus === 'error') {
            return ['error' => $this->_errorResponse($httpCode, $pgResult['message'])];
        }

        return [
            'create_order' => true,
            'message' => __('Checkout session created'),
            'wave_launch_url' => $pgResult['data']['wave_launch_url']
        ];
    }

    /**
     * Process Cash on Delivery payment
     *
     * @param array $data Request data
     * @return array Payment result
     */
    private function _processCodPayment($data)
    {
        $customerCityId = null;

        if (!empty($data['customer_address_id'])) {
            try {
                $address = $this->CustomerAddresses->get($data['customer_address_id']);
                $customerCityId = $address->city_id;
            } catch (\Exception $e) {
                $customerCityId = null;
            }
        }

        // COD not allowed for pickup orders
        if (!empty($data['showroom_id'])) {
            $customerCityId = 0;
        }

        if ((int)$customerCityId !== Configure::read('Constants.ABIDJAN_CITY_ID')) {
            return ['error' => $this->_errorResponse(400, __('Cash on Delivery is not available for the selected city.'))];
        }

        return [
            'create_order' => true,
            'message' => __('Your order has been successfully created.')
        ];
    }

    /**
     * Create order record and related data
     *
     * @param array $data Request data
     * @param int $customerId Customer ID
     * @param int $roleId User role ID
     * @param array $orderCalculation Order calculation data
     * @param array $paymentValidation Payment validation data
     * @return \Cake\Http\Response Order creation response
     */
    private function _createOrderRecord($data, $customerId, $roleId, $orderCalculation, $paymentValidation)
    {
        $orderNumber = $this->Orders->generateUniqueOrderNum();

        // Calculate loyalty points data
        $loyaltyData = $this->_calculateLoyaltyData($data);

        // Prepare order data
        $orderData = [
            'customer_id' => $customerId,
            'order_number' => $orderNumber,
            'order_date' => date('Y-m-d H:i:s'),
            'status' => 'Pending',
            'payment_method' => $paymentValidation['payment_method'],
            'subtotal_amount' => $data['total_amount'],
            'total_amount' => $paymentValidation['final_amount'],
            'delivery_mode' => $data['delivery_mode'],
            'delivery_mode_type' => $data['delivery_mode_type'],
            'wallet_redeem_amount' => $paymentValidation['wallet_amount'],
            'wallet_payment_type' => 'debit',
            'delivery_charge' => $orderCalculation['delivery_charge'],
            'discount_amount' => $orderCalculation['coupon_amount'],
            'loyalty_points_redeemed' => $loyaltyData['points_redeemed'],
            'loyalty_amount' => $loyaltyData['loyalty_amount'],
            'order_online_source' => 'Web',
            'created_by_role' => $roleId,
            'delivery_date' => $this->CalculateEstimatedDelivery(strtolower($data['delivery_mode_type']))
        ];

        // Add optional fields
        if (!empty($data['customer_address_id'])) {
            $orderData['customer_address_id'] = $data['customer_address_id'];
        }

        if (!empty($data['showroom_id'])) {
            $orderData['showroom_id'] = $data['showroom_id'];
        }

        if (!empty($orderCalculation['city_id'])) {
            $orderData['city_id'] = $orderCalculation['city_id'];
        }

        if (!empty($orderCalculation['offer_id'])) {
            $orderData['offer_id'] = $orderCalculation['offer_id'];
        }

        // Add payment status for Waiver payments
        if ($paymentValidation['payment_method'] === 'Waiver') {
            $orderData['payment_status'] = 'paid';
        }

        // Prepare transaction data
        $transactionData = [
            'invoice_number' => $this->Transactions->generateUniqueInvoiceNum(),
            'transaction_number' => $this->Transactions->generateUniqueTransactionNum(),
            'transaction_date' => date('Y-m-d H:i:s'),
            'amount' => $paymentValidation['final_amount'],
            'payment_method' => $paymentValidation['payment_method'],
        ];

        $orderData['order_items'] = $orderCalculation['order_items'];
        $orderData['transactions'] = [$transactionData];

        // Create and save order
        $order = $this->Orders->newEmptyEntity();
        $order = $this->Orders->patchEntity($order, $orderData, [
            'associated' => ['OrderItems', 'Transactions']
        ]);

        $savedOrder = $this->Orders->save($order);

        if (!$savedOrder) {
            return $this->_errorResponse(500, __('Failed to create order. Please try again.'), $order->getErrors());
        }

        // Post-order processing
        $this->_postOrderProcessing($savedOrder, $customerId, $data, $paymentValidation);

        $redirectUrl = Router::url([
            'controller' => 'WebResponse',
            'action' => 'success',
            '?' => ['order_id' => $savedOrder->id]
        ], true);

        $result = [
            'status' => 'success',
            'code' => 200,
            'message' => __('Order created successfully!'),
            'redirect_url' => $redirectUrl,
            'data' => $savedOrder
        ];

        // Add Wave launch URL if applicable
        if (isset($data['wave_launch_url'])) {
            $result['wave_launch_url'] = $data['wave_launch_url'];
        }

        return $this->response->withStatus(200)->withStringBody(json_encode($result));
    }
    /**
     * Handle post-order processing tasks
     *
     * @param object $order Saved order entity
     * @param int $customerId Customer ID
     * @param array $data Request data
     * @param array $paymentValidation Payment validation data
     * @return void
     */
    private function _postOrderProcessing($order, $customerId, $data, $paymentValidation)
    {
        // Handle Credit Payment - Create Cart Credit Payments
        if ($paymentValidation['payment_method'] === 'Credit' && !empty($data['credit_application_id'])) {
            $this->_createCreditPaymentRecords($customerId, $data);
        }

        // Process loyalty points redemption
        if (!empty($data['inputPointVal'])) {
            $this->_processLoyaltyPointsRedemption($customerId, $data);
        }

        // Update wallet balance
        if ($paymentValidation['wallet_amount'] > 0) {
            $this->Wallets->updateWalletBalance(
                $customerId,
                $paymentValidation['wallet_amount'],
                'debit',
                'Order Payment'
            );
        }

        // Clear cart
        $this->_clearCustomerCart($customerId);

        // Send confirmation email
        try {
            $this->WebsiteFunction->OrderConfirmationEmail($order->id);
        } catch (\Exception $e) {
            $this->log('Failed to send order confirmation email: ' . $e->getMessage(), 'warning');
        }
    }

    /**
     * Create credit payment records
     *
     * @param int $customerId Customer ID
     * @param array $data Request data
     * @return void
     */
    private function _createCreditPaymentRecords($customerId, $data)
    {
        $cartId = $this->Carts->getCartItems($customerId);
        if (!$cartId) {
            return;
        }

        foreach ($data['cart_items'] as $cartItem) {
            $cartCreditPaymentData = [
                'cart_id' => $cartId->id,
                'customer_id' => $customerId,
                'product_id' => $cartItem['product_id'],
                'product_variant_id' => $cartItem['product_variant_id'] ?? null,
                'credit_application_id' => $data['credit_application_id'],
                'credit_payment_terms_id' => 1,
                'emi_interest_percentage' => '0.00',
                'emi_interest_amount' => '0.00',
                'total_emi_amount' => $cartItem['item_total'] ?? $cartItem['total'],
                'status' => 'pending'
            ];

            $cartCreditPayment = $this->CartCreditPayments->newEntity($cartCreditPaymentData);
            $this->CartCreditPayments->save($cartCreditPayment);
        }
    }

    /**
     * Process loyalty points redemption based on customer group conversion rates
     *
     * @param int $customerId Customer ID
     * @param array $data Request data
     * @return void
     */
    private function _processLoyaltyPointsRedemption($customerId, $data)
    {
        // Only process if loyalty points are being redeemed
        if ((empty($data['redeem_points']) || $data['redeem_points'] <= 0) &&
            (empty($data['inputPointVal']) || $data['inputPointVal'] <= 0)) {
            return;
        }

        $loyaltyDetails = $this->Loyalty->calculateLoyaltyPoints($customerId);
        $currentPoints = (float)str_replace(',', '', $loyaltyDetails['data']['points'] ?? 0);

        // Get the actual points to deduct from customer's balance
        $pointsToDeduct = 0;
        $currencyAmountSpent = 0;

        if (!empty($data['redeem_points']) && $data['redeem_points'] > 0) {
            // Use the actual loyalty points to deduct
            $pointsToDeduct = (float)$data['redeem_points'];

            // Currency amount spent (for spent_amount field)
            $currencyAmountSpent = !empty($data['inputPointVal']) ? (float)$data['inputPointVal'] : $pointsToDeduct;
        } elseif (!empty($data['inputPointVal']) && $data['inputPointVal'] > 0) {
            // Fallback: if only currency amount provided, assume 1:1 ratio
            $currencyAmountSpent = (float)$data['inputPointVal'];
            $pointsToDeduct = $currencyAmountSpent;
        }

        // Calculate remaining points after deduction
        $remainingPoints = max(0, $currentPoints - $pointsToDeduct);

        // Create loyalty record with updated balance
        $loyaltyRecord = [
            'customer_id' => $customerId,
            'loyalty_category' => $loyaltyDetails['data']['loyalty_category'],
            'spent_amount' => $currencyAmountSpent,  // Currency amount spent (for accounting)
            'points' => $remainingPoints,            // Remaining points after deduction
            'validity' => date('Y-m-d H:i:s', strtotime('+3 months')),
            'status' => 'A'
        ];

        $this->Loyalty->customerLoyaltyInsert($loyaltyRecord);

        // Log the loyalty transaction for debugging
        $this->log("Loyalty points redeemed - Customer: {$customerId}, Points deducted: {$pointsToDeduct}, Currency spent: {$currencyAmountSpent}, Remaining: {$remainingPoints}", 'info');
    }

    /**
     * Clear customer cart after successful order
     *
     * @param int $customerId Customer ID
     * @return void
     */
    private function _clearCustomerCart($customerId)
    {
        $guestToken = $this->request->getSession()->read('cartId') ?? null;
        $cartConditions = $customerId ? ['customer_id' => $customerId] : ['guest_token' => $guestToken];

        $cart = $this->Carts->find()
            ->where($cartConditions)
            ->contain(['CartItems'])
            ->first();

        if ($cart && !empty($cart->cart_items)) {
            foreach ($cart->cart_items as $cartItem) {
                $this->Carts->CartItems->delete($cartItem);
            }
        }
    }

    /**
     * Calculate loyalty points data for order based on customer group conversion rates
     *
     * @param array $data Request data
     * @return array Loyalty data
     */
    private function _calculateLoyaltyData($data)
    {
        $loyaltyPointsRedeemed = 0;
        $loyaltyAmount = 0;

        // Check if loyalty points are being redeemed
        if (!empty($data['inputPointVal']) && $data['inputPointVal'] > 0) {
            // inputPointVal contains the currency amount for discount
            $loyaltyAmount = (float)$data['inputPointVal'];

            // redeem_points contains the actual loyalty points to deduct from customer balance
            if (!empty($data['redeem_points']) && $data['redeem_points'] > 0) {
                $loyaltyPointsRedeemed = (float)$data['redeem_points'];
            } else {
                // Fallback: if redeem_points not provided, assume 1:1 ratio
                $loyaltyPointsRedeemed = $loyaltyAmount;
            }
        } elseif (!empty($data['redeem_points']) && $data['redeem_points'] > 0) {
            // Fallback: if only redeem_points provided, assume 1:1 ratio
            $loyaltyPointsRedeemed = (float)$data['redeem_points'];
            $loyaltyAmount = $loyaltyPointsRedeemed;
        }

        return [
            'points_redeemed' => $loyaltyPointsRedeemed,  // Actual points to deduct from customer
            'loyalty_amount' => $loyaltyAmount            // Currency amount for order discount
        ];
    }

    /**
     * Generate standardized error response
     *
     * @param int $code HTTP status code
     * @param string $message Error message
     * @param array $errors Additional error details
     * @return \Cake\Http\Response Error response
     */
    private function _errorResponse($code, $message, $errors = [])
    {
        $result = [
            'status' => 'error',
            'code' => $code,
            'message' => $message
        ];

        if (!empty($errors)) {
            $result['errors'] = $errors;
        }

        return $this->response->withStatus($code)->withStringBody(json_encode($result));
    }



    public function getDeliveryChargeFromCityAPI($cityId, $deliveryMode, $weightQuantityArray)
    {
        $cityId = $cityId;
        $deliveryMode = $deliveryMode;
        $weightQuantityArray = $weightQuantityArray;

        if (empty($weightQuantityArray) || !is_array($weightQuantityArray)) {
            return [
                'status' => 'error',
                'message' => __('Invalid weight and quantity data.')
            ];
        }

        $deliveryCharges = $this->DeliveryCharges->find()
            ->where([
                'city_id' => $cityId,
                'delivery_mode' => $deliveryMode,
                'status' => 'A'
            ])
            ->order(['weight' => 'ASC'])
            ->toArray();

        if (empty($deliveryCharges)) {
            return [
                'status' => 'error',
                'message' => __('No delivery charges found for the selected criteria.')
            ];
        }

        $total_delivery_charge = 0.00;

        foreach ($weightQuantityArray as $item) {
            $weight = $item['weight'];
            $quantity = $item['quantity'];
            $applicableCharge = null;

            foreach ($deliveryCharges as $charge) {
                if ($weight > $charge->weight) {
                    $applicableCharge = $charge;
                } else {
                    break;
                }
            }

            if ($applicableCharge) {
                $quotient = $quantity / 2;
                $remainder = $quantity % 2;
                $calculated_charge = ($remainder == 0) ? ($quotient * $applicableCharge->charge) : (($quotient * $applicableCharge->charge) + (1 * $applicableCharge->charge));
                $total_delivery_charge += $calculated_charge;
            }
        }

        $result = [
            'status' => 'success',
            'total_delivery_charge' => $total_delivery_charge,
            'message' => __('Shipping charge applied.')
        ];

        return $result;
    }

    public function getDeliveryChargeFromCityAPINew($cityId, $deliveryMode, $weightQuantityArray, $sizeQuantityArray)
    {
        $cityId = $cityId;
        $deliveryMode = $deliveryMode;
        $weightQuantityArray = $weightQuantityArray;
        $sizeQuantityArray = $sizeQuantityArray;

        if (empty($weightQuantityArray) || !is_array($weightQuantityArray)) {
            return [
                'status' => 'error',
                'message' => __('Invalid weight and quantity data.')
            ];
        }


        // $cityId = $this->request->getData('cityId');
        // $deliveryMode = $this->request->getData('delivery_mode');
        // $weightQuantityArray = $this->request->getData('weightQuantityArray');
        // $sizeQuantityArray = $this->request->getData('sizeQuantityArray');

        $result = $this->DeliveryCharges->calculateDeliveryCharge($cityId, $deliveryMode, $weightQuantityArray, $sizeQuantityArray);
        $result['message'] = __('Shipping charge applied.');

        $result = [
            'status' => 'success',
            'total_delivery_charge' => $result['total_delivery_charge'] ?? 0,
            'message' => __('Shipping charge applied.')
        ];

        return $result;
    }

    public function getDeliveryChargeFromCityNew()
    {
        $this->request->allowMethod(['post']);

        $cityId = $this->request->getData('cityId');
        $deliveryMode = $this->request->getData('delivery_mode');
        $weightQuantityArray = $this->request->getData('weightQuantityArray');
        $sizeQuantityArray = $this->request->getData('sizeQuantityArray');

        // Validate required parameters
        if (empty($cityId) || $cityId === '0' || $cityId === 0) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('Please select a delivery address.')
            ]));
        }

        if (empty($deliveryMode)) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('Please select a delivery mode.')
            ]));
        }

        $result = $this->DeliveryCharges->calculateDeliveryCharge($cityId, $deliveryMode, $weightQuantityArray, $sizeQuantityArray);
        
        // Add additional context to the result
        if ($result['status'] === 'success') {
            $result['message'] = __('Shipping charge applied.');
            $result['showroom'] = $cityId == 0 ? true : false;
        }
        
        return $this->response->withType('application/json')->withStringBody(json_encode($result));
    }

    public function getDeliveryChargeFromCity()
    {
        $this->request->allowMethod(['post']);
        $data = $this->request->getData();

        $cityId = $data['cityId'];
        $deliveryMode = $data['deliveryMode'];
        $weightQuantityArray = $data['weightQuantityArray'];

        if (empty($weightQuantityArray) || !is_array($weightQuantityArray)) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('Invalid weight and quantity data.')
            ]));
        }

        $deliveryCharges = $this->DeliveryCharges->find()
            ->where([
                'city_id' => $cityId,
                'delivery_mode' => $deliveryMode,
                'status' => 'A'
            ])
            ->order(['weight' => 'ASC'])
            ->toArray();

        if (empty($deliveryCharges)) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('No delivery charges found for the selected criteria.')
            ]));
        }

        $total_delivery_charge = 0.00;

        foreach ($weightQuantityArray as $item) {
            $weight = $item['weight'];
            $quantity = $item['quantity'];
            $applicableCharge = null;

            foreach ($deliveryCharges as $charge) {
                if ($weight > $charge->weight) {
                    $applicableCharge = $charge;
                } else {
                    break;
                }
            }

            if ($applicableCharge) {
                $quotient = $quantity / 2;
                $remainder = $quantity % 2;
                $calculated_charge = ($remainder == 0) ? ($quotient * $applicableCharge->charge) : (($quotient * $applicableCharge->charge) + (1 * $applicableCharge->charge));
                $total_delivery_charge += $calculated_charge;
            }
        }

        $result = [
            'status' => 'success',
            'total_delivery_charge' => $total_delivery_charge,
            'message' => __('Shipping charge applied.')
        ];

        $this->response = $this->response->withType('application/json')->withStringBody(json_encode($result));
        return $this->response;
    }

    private function CalculateEstimatedDelivery($deliveryMode)
    {
        $now = new DateTime();
        $currentHour = (int)$now->format('H');

        $siteSettings = $this->SiteSettings->find()
            ->select(['express_delivery_order_cutoff_time'])
            ->first();
        $express_delivery_order_cutoff_time = (int)$siteSettings->express_delivery_order_cutoff_time;

        if ($deliveryMode === 'express') {
            if ($currentHour >= $express_delivery_order_cutoff_time) {
                $now->modify('+1 day'); // Move to next day if after cutoff
            }
        } else {
            $now->modify('+2 days'); // Standard delivery in 48 hours
        }

        return $now->format('Y-m-d h:i:s');
    }

    /*****  Checkout Page Code end *******/

    /**
     * Delete an address
     *
     * @param int|null $id Address ID
     * @return \Cake\Http\Response JSON response
     */
    public function deleteAddressAi($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $this->response = $this->response->withType('application/json');

        if (!$id) {
            return $this->response->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'Invalid address ID'
            ]));
        }

        $customerAddress = $this->CustomerAddresses->get($id);

        // Check if the address belongs to the logged-in customer
        $identity = $this->request->getSession()->read('Auth.User');
        $customerId = $identity ? $identity->id : null;

        if (!$customerId) {
            return $this->response->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'You must be logged in to delete an address'
            ]));
        }

        $user = $this->Users->find()
            ->contain(['Customers'])
            ->where(['Users.id' => $customerId])
            ->first();

        if (!$user || !$user->customer || $customerAddress->customer_id != $user->customer->id) {
            return $this->response->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'You do not have permission to delete this address'
            ]));
        }

        // Instead of deleting, update the status to 'D' (soft delete)
        $customerAddress->status = 'D';
        if ($this->CustomerAddresses->save($customerAddress)) {
            return $this->response->withStringBody(json_encode([
                'status' => 'success',
                'message' => 'Address deleted successfully'
            ]));
        } else {
            return $this->response->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'The address could not be deleted. Please try again.'
            ]));
        }
    }

    /*****  Calculate Cart Total  *******/
    private function calculateCartTotal($cartId)
    {
        $cart = $this->Carts->find()
            ->where(['id' => $cartId])
            ->contain(['CartItems' => ['Products', 'ProductVariants']])
            ->first();

        if (!$cart) {
            return [
                'status' => false,
                'message' => 'Cart not found',
                'data' => []
            ];
        }

        $totalPrice = 0;
        $totalDiscountedPrice = 0;
        $cartItems = [];
        $cartOutItems = [];

        foreach ($cart->cart_items as $item) {
            $price = null;
            $saleprice = null;

            // Get price from variant if exists
            if ($item->product_variant_id) {
                $productVariant = $this->CartItems->ProductVariants->find()
                    ->select(['promotion_price', 'sales_price'])
                    ->where(['id' => $item->product_variant_id])
                    ->first();

                $price = $productVariant ? $productVariant->promotion_price : null;
                $saleprice = $productVariant ? $productVariant->sales_price : null;
            }

            // Get price from product if no variant price
            if (!$price) {
                $product = $this->CartItems->Products->find()
                    ->select(['promotion_price', 'sales_price'])
                    ->where(['id' => $item->product_id])
                    ->first();

                $price = $product ? $product->promotion_price : null;
                $saleprice = $product ? $product->sales_price : null;
            }

            if ($price !== null) {
                $getAvailableStatus = $this->Products->singleProductStockStatus($item->product_id, $item->product_variant_id, $item->product_attribute_id);

                if ($getAvailableStatus == "In Stock") {
                    $totalPrice += number_format($item->quantity * $price, 2, '.', '');
                    $totalDiscountedPrice += number_format($item->quantity * $saleprice, 2, '.', '');
                }
            }
        }

        return [
            'status' => true,
            'data' => [
                'total_price' => $totalPrice,
                'total_discounted_price' => $totalDiscountedPrice,
                'cart_id' => $cart->id
            ]
        ];
    }

    /****  My account order ******/
    public function authenticateUser()
    {

        return true;
    }

    public function order()
    {

        if (empty($this->request->getSession()->read('Auth.User'))) {
            $this->Flash->toast(__("Login required to access user account."), [
                'element' => 'toast',
                'params' => ['type' => 'warning']
            ]);
            return $this->redirect(['controller' => 'customer', 'action' => 'login']);
        }

        $categoriesQuery = $this->OrderReturnCategories->getAllCategories();
        $categoriesArray = $categoriesQuery->toArray();


        $OrderReturnCategories = $this->OrderReturnCategories->getAllCategories();
        $OrderReturnCategoriesArray = $OrderReturnCategories->toArray();



        $data = $this->request->getData();
        $guestToken = $this->request->getSession()->read('cartId') ?? null;

        $identity = $this->request->getSession()->read('Auth.User');
        // Generate Guest Token if not logged in and no guest-token exists
        if (!$identity && !$guestToken) {
            $guestToken = Text::uuid();
            $this->request->getSession()->write('cartId', $guestToken);
        }

        // Determine Customer ID or Use Guest Token
        $customerId = $identity ? $identity->id : null;
        $customerAddress = "";
        if (!empty($customerId)) {
            $users = $this->Users->find()
                ->contain(['Customers']) // Include related Customers
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $customerId])
                ->first();
            $customerId = $users->customer->id;
            $customerAddress = $this->CustomerAddresses->listAddress($customerId);
        }

        $page = (int)$this->request->getQuery('page', 1);
        $limit = 20;
        $orderList = $this->Orders->orderListByUser($customerId, $page, $limit);

        foreach ($orderList as &$order) {
            foreach ($order->order_items as &$item) {
                $image = $this->Products->getProductThumbnailImage($item->product_id, $item->product_variant_id);
                if ($image) {
                    $image = $this->Media->getCloudFrontURL($image);
                }
                $item['thumb_image'] = $image;
            }
        }

        if ($this->request->is('ajax')) {
            $this->viewBuilder()->disableAutoLayout();
            $this->set(compact('orderList'));
            $this->render('order_items');
            return;
        }

        $this->set(compact('customerAddress','customerId', 'orderList','categoriesArray','OrderReturnCategoriesArray'));
        $this->viewBuilder()->setTemplatePath('account');
        $this->render('order');
    }

    /****  My account order End ******/

    public function success()
    {
        $this->viewBuilder()->setTemplatePath('Payments');
        $this->render('success');
    }

    public function address()
    {

        $auth = $this->request->getSession()->read('Auth.User');
        $users = $this->Users->find()
            ->contain(['Customers']) // Include related Customers
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $auth->id])
            ->first();

        $addresses = $this->CustomerAddresses->listAddress($users->customer->id);


        $this->set(compact('addresses'));
        $this->viewBuilder()->setTemplatePath('account');
        $this->render('address');
    }

    public function addAddress()
    {
        $auth = $this->request->getSession()->read('Auth.User');
        if (!$auth) {
            return $this->redirect(['action' => 'login']);
        }

        $users = $this->Users->find()
            ->contain(['Customers'])
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $auth->id])
            ->first();

        $address = $this->CustomerAddresses->newEmptyEntity();

        if ($this->request->is('post')) {
            $data = $this->request->getData();
            $data['customer_id'] = $users->customer->id;
            $data['status'] = 'A'; // Set default status as Active

            $address = $this->CustomerAddresses->patchEntity($address, $data);

            if ($this->CustomerAddresses->save($address)) {
                $this->Flash->websiteSuccess(__('The address has been saved.'));
                return $this->redirect(['action' => 'address']);
            }
            $this->Flash->error(__('The address could not be saved. Please try again.'));

            // Debug validation errors
            if ($address->getErrors()) {
                $this->Flash->error(json_encode($address->getErrors()));
            }
        }

        // Load states and cities
        $states = $this->CustomerAddresses->States->find('list', ['limit' => 200])->all();
        $cities = $this->CustomerAddresses->Cities->find('list', ['limit' => 200])->all();

        $this->set(compact('address', 'states', 'cities'));
        $this->viewBuilder()->setTemplatePath('account');
        $this->render('add_address'); // We can reuse the edit form for adding new address
    }


    public function checkoutEditAddress($id = null)
    {
        $this->response = $this->response->withType('application/json');
        $auth = $this->request->getSession()->read('Auth.User');

        if (!$auth) {
            return $this->response->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'You must be logged in to edit an address'
            ]));
        }

        // Get the customer ID from the user
        $users = $this->Users->find()
            ->contain(['Customers'])
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $auth->id])
            ->first();

        if (!$users || empty($users->customer)) {
            return $this->response->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'Customer not found'
            ]));
        }

        $customerId = $users->customer->id;

        if ($this->request->is(['get'])) {

            // Handle GET request - return address details


            $address = $this->CustomerAddresses->getSingleAddress($id, $customerId);


            if (!$address) {
                return $this->response->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'Address not found'
                ]));
            }

            return $this->response->withStringBody(json_encode([
                'status' => 'success',
                'address' => $address
            ]));
        } elseif ($this->request->is(['post', 'put', 'patch'])) {
            // Handle POST request - update address
            $address = $this->CustomerAddresses->get($id);

            // Verify the address belongs to the customer
            if ($address->customer_id != $customerId) {
                return $this->response->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'You do not have permission to edit this address'
                ]));
            }

            $data = $this->request->getData();
            $address = $this->CustomerAddresses->patchEntity($address, $data);

            if ($this->CustomerAddresses->save($address)) {
                return $this->response->withStringBody(json_encode([
                    'status' => 'success',
                    'message' => 'Address updated successfully',
                    'address' => $address
                ]));
            } else {
                return $this->response->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'The address could not be updated. Please try again.',
                    'errors' => $address->getErrors()
                ]));
            }
        }

        return $this->response->withStringBody(json_encode([
            'status' => 'error',
            'message' => 'Invalid request method'
        ]));
    }


    public function checkoutAddAddress()
    {
        $this->response = $this->response->withType('application/json');
        $auth = $this->request->getSession()->read('Auth.User');

        if (!$auth) {
            return $this->response->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'You must be logged in to add an address'
            ]));
        }

        // Get the customer ID from the user
        $users = $this->Users->find()
            ->contain(['Customers'])
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $auth->id])
            ->first();

        if (!$users || empty($users->customer)) {
            return $this->response->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'Customer not found'
            ]));
        }

        $customerId = $users->customer->id;

        if ($this->request->is(['post'])) {
            $address = $this->CustomerAddresses->newEmptyEntity();
            $data = $this->request->getData();
            $data['customer_id'] = $customerId;

            $address = $this->CustomerAddresses->patchEntity($address, $data);

            if ($this->CustomerAddresses->save($address)) {
                return $this->response->withStringBody(json_encode([
                    'status' => 'success',
                    'message' => 'Address added successfully',
                    'address' => $address
                ]));
            } else {
                return $this->response->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'The address could not be added. Please try again.',
                    'errors' => $address->getErrors()
                ]));
            }
        }

        return $this->response->withStringBody(json_encode([
            'status' => 'error',
            'message' => 'Invalid request method'
        ]));
    }


    public function checkoutDeleteAddress($id = null)
    {
        $this->response = $this->response->withType('application/json');
        $auth = $this->request->getSession()->read('Auth.User');

        if (!$auth) {
            return $this->response->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'You must be logged in to delete an address'
            ]));
        }

        // Get the customer ID from the user
        $users = $this->Users->find()
            ->contain(['Customers'])
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $auth->id])
            ->first();

        if (!$users || empty($users->customer)) {
            return $this->response->withStringBody(json_encode([
                'status' => 'error',
                'message' => 'Customer not found'
            ]));
        }

        $customerId = $users->customer->id;

        if ($this->request->is(['post', 'delete'])) {
            $address = $this->CustomerAddresses->find()
                ->where(['id' => $id, 'customer_id' => $customerId])
                ->first();

            if (!$address) {
                return $this->response->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'Address not found or you do not have permission to delete it'
                ]));
            }

            // Instead of deleting, update the status to 'D' (soft delete)
            $address->status = 'D';
            if ($this->CustomerAddresses->save($address)) {
                return $this->response->withStringBody(json_encode([
                    'status' => 'success',
                    'message' => 'Address deleted successfully'
                ]));
            } else {
                return $this->response->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => 'The address could not be deleted. Please try again.'
                ]));
            }
        }

        return $this->response->withStringBody(json_encode([
            'status' => 'error',
            'message' => 'Invalid request method'
        ]));
    }

    public function editAddress($id = null)
    {
        $auth = $this->request->getSession()->read('Auth.User');
        if (!$auth) {
            return $this->redirect(['action' => 'login']);
        }

        $address = $this->CustomerAddresses->get($id, [
            //  'contain' => ['Cities', 'States']
        ]);
        // dd($address);

        if ($this->request->is(['patch', 'post', 'put'])) {
            $data = $this->request->getData();
            $address = $this->CustomerAddresses->patchEntity($address, $data);

            if ($this->CustomerAddresses->save($address)) {
                $this->Flash->websiteSuccess(__('The address has been updated.'));
                return $this->redirect(['action' => 'address']);
            }
            $this->Flash->error(__('The address could not be saved. Please try again.'));
        }
        $states = $this->CustomerAddresses->States->find('list', ['limit' => 200])->all();
        $cities = $this->CustomerAddresses->Cities->find('list', ['limit' => 200])->all();
        $this->set(compact('address', 'states', 'cities'));
        $this->viewBuilder()->setTemplatePath('account');
        $this->render('edit_address');
    }

    public function deleteAddress($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);
        $auth = $this->request->getSession()->read('Auth.User');
        if (!$auth) {
            return $this->redirect(['action' => 'login']);
        }

        $address = $this->CustomerAddresses->get($id);

        // Verify if the address belongs to the logged-in user's customer
        $users = $this->Users->find()
            ->contain(['Customers'])
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $auth->id])
            ->first();

        if ($address->customer_id !== $users->customer->id) {
            $this->Flash->error(__('You are not authorized to delete this address.'));
            return $this->redirect(['action' => 'address']);
        }

        // Instead of deleting, update the status to 'D' (soft delete)
        $address->status = 'D';
        if ($this->CustomerAddresses->save($address)) {
            $this->Flash->websiteSuccess(__('The address has been deleted.'));
        } else {
            $this->Flash->websiteError(__('The address could not be deleted. Please try again.'));
        }

        return $this->redirect(['action' => 'address']);
    }

    public function favourite()
    {
        if (empty($this->request->getSession()->read('Auth.User'))) {
            $this->Flash->toast(__("Login required to access user account."), [
                'element' => 'toast',
                'params' => ['type' => 'warning']
            ]);
            return $this->redirect(['controller' => 'customer', 'action' => 'login']);
        }

        $auth = $this->request->getSession()->read('Auth.User');
        $users = $this->Users->find()
            ->contain(['Customers']) // Include related Customers
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $auth->id])
            ->first();

        $wishlists = $this->Wishlists->viewWishlist($users->customer->id);

        if ($wishlists) {

            foreach ($wishlists as &$wishlist) {

                $wishlist['product']['rating'] = $this->Reviews->getAverageRating($wishlist['product']['id']);
                $wishlist['product']['total_review'] = $this->Reviews->getTotalReviews($wishlist['product']['id']);
                $wishlist['product']['discount'] = $this->Products->getDiscount($wishlist['product']['id']);

                $wishlist['product']['product_image'] = '';
                $image = $this->ProductImages->getDefaultProductImage($wishlist['product']['id']);
                if ($image) {
                    $wishlist['product']['product_image'] = $this->Media->getCloudFrontURL($image);
                }
            }
        }

        $this->set(compact('wishlists'));
        $this->viewBuilder()->setTemplatePath('account');
        $this->render('favourite');
    }

    public function removeWishlistData()
    {
        $this->request->allowMethod(['post']); // Ensure only POST requests are allowed

        $auth = $this->request->getSession()->read('Auth.User');
        if (!$auth) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('User not authenticated.')
            ]));
        }

        $customer_id = $auth['id']; // Get the customer ID from the session
        $product_id = $this->request->getData('product_id'); // Get product ID from request body
        $product_attribute_id = $this->request->getData('product_attribute_id'); // Get product attribute ID from request body

        if (!$product_id) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('Invalid product ID.')
            ]));
        }

        $removed = $this->Wishlists->removeFromWishlist($customer_id, $product_id, $product_attribute_id);

        if ($removed) {
            $result = [
                'status' => 'success',
                'message' => __('Removed successfully from wishlist.')
            ];
        } else {
            $result = [
                'status' => 'error',
                'message' => __('Failed to remove from wishlist.')
            ];
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($result));
    }

    public function wallet()
    {
        if (empty($this->request->getSession()->read('Auth.User'))) {
            $this->Flash->toast(__("Login required to access user account."), [
                'element' => 'toast',
                'params' => ['type' => 'warning']
            ]);
            return $this->redirect(['controller' => 'customer', 'action' => 'login']);
        }

        $auth = $this->request->getSession()->read('Auth.User');
        $users = $this->Users->find()
            ->contain(['Customers']) // Include related Customers
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $auth->id])
            ->first();

        $loyaltyDetails = $this->Loyalty->calculateLoyaltyPoints($users->customer->id);
        $customerId = $users->customer->id;

        $wallet = $this->Wallets->getMyWalletAmount($customerId);
        if ($wallet) {
            $walletAmount = $wallet->balance ?? 0.00; // Use null coalescing to handle cases where balance is not set
        } else {
            $walletAmount = 0.00; // Default to 0 if no wallet found
        }

        // Lazy load: only load first page of transactions, default filter 'all'
        $filter = $this->request->getQuery('filter', 'all');
        $page = (int)$this->request->getQuery('page', 1);
        $limit = 10;
        $transactions = $this->Orders->WalletOrderTransactionsPaginated($customerId, $filter, $page, $limit);
     
        $this->set(compact('loyaltyDetails','users', 'walletAmount','transactions', 'filter', 'page', 'limit'));
        $this->viewBuilder()->setTemplatePath('account');
        $this->render('wallet');
    }

    // AJAX endpoint for lazy loading/filtering wallet transactions
    public function walletTransactionsAjax()
    {
        $this->request->allowMethod(['get']);
        $this->response = $this->response->withType('application/json');

        if (empty($this->request->getSession()->read('Auth.User'))) {
            return $this->response->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('Login required to access wallet.')
            ]));
        }

        $auth = $this->request->getSession()->read('Auth.User');
        $users = $this->Users->find()
            ->contain(['Customers'])
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $auth->id])
            ->first();

        $customerId = $users->customer->id;
        $filter = $this->request->getQuery('filter', 'all');
        $page = (int)$this->request->getQuery('page', 1);
        $limit = (int)$this->request->getQuery('limit', 10);

        $transactions = $this->Orders->WalletOrderTransactionsPaginated($customerId, $filter, $page, $limit);

        return $this->response->withStringBody(json_encode([
            'status' => 'success',
            'transactions' => $transactions
        ]));
    }



    public function card3()
    {

        $auth = $this->request->getSession()->read('Auth.User');
        $users = $this->Users->find()
            ->contain(['Customers']) // Include related Customers
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $auth->id])
            ->first();

        $wishlists = $this->Wishlists->viewWishlist($users->customer->id);

        if ($wishlists) {

            foreach ($wishlists as &$wishlist) {

                $wishlist['product']['rating'] = $this->Reviews->getAverageRating($wishlist['product']['id']);
                $wishlist['product']['total_review'] = $this->Reviews->getTotalReviews($wishlist['product']['id']);
                $wishlist['product']['discount'] = $this->Products->getDiscount($wishlist['product']['id']);

                $wishlist['product']['product_image'] = '';
                $image = $this->ProductImages->getDefaultProductImage($wishlist['product']['id']);
                if ($image) {
                    $wishlist['product']['product_image'] = $this->Media->getCloudFrontURL($image);
                }
            }
        }
        $this->set(compact('wishlists'));
        $this->viewBuilder()->setTemplatePath('account');
        $this->render('card');
    }

    public function deleteAccount()
    {
        $this->request->allowMethod(['post']);
        $auth = $this->request->getSession()->read('Auth.User.id');
        $usersTable = TableRegistry::getTableLocator()->get('Users');
        $user = $usersTable->get($auth);
        $user->status = 'D';
        if ($usersTable->delete($user)) {
            $this->WebsiteFunction->userAccountDeleteEmail($user->email, $user->first_name . ' ' . $user->last_name, '');
            $this->Flash->websiteSuccess(__('Your account has been deleted successfully.'));
            return $this->redirect(['controller' => 'Website', 'action' => 'deletelogout']);
        } else {
            $this->Flash->websiteError(__('Unable to delete your account. Please try again.'));
            return $this->redirect(['action' => 'account']);
        }
    }

    public function card()
    {
        // Ensure user is logged in
        $auth = $this->request->getSession()->read('Auth.User');
        $users = $this->Users->find()
            ->contain(['Customers']) // Include related Customers
            ->where(['Users.status' => 'A'])
            ->where(['Users.id' => $auth->id])
            ->first();
       // dd($users->customer->id);

        $account = $users->customer->id;
        if (!$account) {
            $this->Flash->websiteError(__('Please login to view your cards.'));
            return $this->redirect(['controller' => 'Customer', 'action' => 'login']);
        }

        // Fetch cards for the logged-in customer
        $cards = $this->CustomerCards->find('all')
            ->where(['customer_id' => $users->customer->id])
            ->order(['created' => 'DESC'])
            ->toArray();

        $this->set(compact('cards'));
        $this->viewBuilder()->setTemplatePath('account');
        $this->render('card');
    }

    public function cardAdd()
    {
        // Ensure user is logged in
        $account = $this->request->getSession()->read('Auth.User');
        if (!$account) {
            $this->Flash->websiteError(__('Please login to add a card.'));
            return $this->redirect(['controller' => 'Customer', 'action' => 'login']);
        }
        $users = $this->Users->find()
        ->contain(['Customers']) // Include related Customers
        ->where(['Users.status' => 'A'])
        ->where(['Users.id' => $account->id])
        ->first();

        $account = $users->customer->id;

        if ($this->request->is('post')) {
            $cardData = $this->request->getData();
            $cardData['customer_id'] = $account;

            // Format expiry date to MM/YY
            if (!empty($cardData['expiry_month']) && !empty($cardData['expiry_year'])) {
                $cardData['expiry_date'] = sprintf('%02d/%02d',
                    $cardData['expiry_month'],
                    substr($cardData['expiry_year'], -2)
                );
            }

            $card = $this->CustomerCards->newEntity($cardData);

            if ($this->CustomerCards->save($card)) {
                $this->Flash->websiteSuccess(__('Card has been saved.'));
                return $this->redirect(['action' => 'card']);
            }
            $this->Flash->websiteError(__('Unable to save the card. Please check the details and try again.'));
        }

        $this->viewBuilder()->setTemplatePath('account');
        $this->render('card_add');
    }

    public function cardEdit($id = null)
    {
        // Ensure user is logged in
        $account = $this->request->getSession()->read('Auth.User');
        $users = $this->Users->find()
        ->contain(['Customers']) // Include related Customers
        ->where(['Users.status' => 'A'])
        ->where(['Users.id' => $account->id])
        ->first();

        $account = $users->customer->id;

        if (!$account) {
            $this->Flash->websiteError(__('Please login to edit a card.'));
            return $this->redirect(['controller' => 'Customer', 'action' => 'login']);
        }

        // Find the card and ensure it belongs to the logged-in customer
        $card = $this->CustomerCards->find()
            ->where([
                'id' => $id,
                'customer_id' => $account
            ])
            ->first();
        // Parse existing expiry date
        if (!empty($card->expiry_date)) {
            // Assuming expiry_date is in 'MM/YY' format
            list($month, $year) = explode('/', $card->expiry_date);
            $card->expiry_month = (int)$month;
            $card->expiry_year = '20' . $year; // Prepend '20' to make it a full year
        }

        if (!$card) {
            $this->Flash->websiteError(__('Card not found or you are not authorized to edit this card.'));
            return $this->redirect(['action' => 'card']);
        }

        if ($this->request->is(['patch', 'post', 'put'])) {
            $cardData = $this->request->getData();
            $cardData['customer_id'] = $account;

            // Format expiry date to MM/YY
            if (!empty($cardData['expiry_month']) && !empty($cardData['expiry_year'])) {
                $cardData['expiry_date'] = sprintf('%02d/%02d',
                    $cardData['expiry_month'],
                    substr($cardData['expiry_year'], -2)
                );
            }

            $card = $this->CustomerCards->patchEntity($card, $cardData);

            if ($this->CustomerCards->save($card)) {
                $this->Flash->websiteSuccess(__('Card has been updated.'));
                return $this->redirect(['action' => 'card']);
            }
            $this->Flash->websiteError(__('Unable to update the card. Please check the details and try again.'));
        }

        $this->set(compact('card'));
        $this->viewBuilder()->setTemplatePath('account');
        $this->render('card_edit');
    }

    public function cardDelete($id = null)
    {
        $this->request->allowMethod(['post', 'delete']);

        // Ensure user is logged in
        $account = $this->request->getSession()->read('Auth.User');
        if (!$account) {
            $this->Flash->websiteError(__('Please login to delete a card.'));
            return $this->redirect(['controller' => 'Customer', 'action' => 'login']);
        }
        $users = $this->Users->find()
        ->contain(['Customers']) // Include related Customers
        ->where(['Users.status' => 'A'])
        ->where(['Users.id' => $account->id])
        ->first();

        $account = $users->customer->id;

        // Find the card and ensure it belongs to the logged-in customer
        $card = $this->CustomerCards->find()
            ->where([
                'id' => $id,
                'customer_id' => $account
            ])
            ->first();

        if (!$card) {
            $this->Flash->websiteError(__('Card not found or you are not authorized to delete this card.'));
            return $this->redirect(['action' => 'card']);
        }

        if ($this->CustomerCards->delete($card)) {
            $this->Flash->websiteSuccess(__('Card has been deleted.'));
        } else {
            $this->Flash->websiteError(__('Unable to delete the card. Please try again.'));
        }

        return $this->redirect(['action' => 'card']);
    }

    public function orderCancelWeb()
    {

        $result = [];
        $data = $this->request->getData();
        $orderId = $data['order_id'];
        if (!$this->request->is('post')) {
            return $this->response->withType('application/json')->withStatus(405)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 405,
                    'message' => __('Method not allowed')
                ]));
        }



        $identity = $this->request->getSession()->read('Auth.User');

        if (!$identity) {
            return $this->response->withType('application/json')->withStatus(401)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 401,
                    'message' => __('User is not authenticated')
                ]));
        }

        if (empty($orderId)) {
            return $this->response->withType('application/json')->withStatus(400)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 400,
                    'message' => __('Order ID is required')
                ]));
        }

        try {
            $order = $this->Orders->get($orderId, ['contain' => ['OrderItems']]);
        } catch (\Cake\Datasource\Exception\RecordNotFoundException $e) {
            return $this->response->withType('application/json')->withStatus(404)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 404,
                    'message' => __('Order not found')
                ]));
        }

        // $customerId = $identity->get('_matchingData')['Customers']['id'];

        $users = $this->Users->find()
        ->contain([
            'Customers' => function ($q) {
                return $q->select(['id']); // Select only the Customer.id field
            }
        ])
        ->select(['Users.id']) // Select the necessary fields from Users
        ->where(['Users.status' => 'A'])
        ->where(['Users.id' => $identity->id])
        ->first();

        $customerId = $users->customer->id;


        if ($order->customer_id !== $customerId) {
            return $this->response->withType('application/json')->withStatus(403)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 403,
                    'message' => __('Unauthorized to cancel this order')
                ]));
        }

        if ($order->payment_method == 'Credit') {
            return $this->response->withType('application/json')->withStatus(400)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 400,
                    'message' => __('The order cannot be canceled as it was made on credit')
                ]));
        }

        foreach ($order->order_items as $item) {
            if (in_array($item->status, ['Shipped', 'Delivered'])) {
                return $this->response->withType('application/json')->withStatus(400)
                    ->withStringBody(json_encode([
                        'status' => 'error',
                        'code' => 400,
                        'message' => __('Cannot cancel order as one or more items have been shipped/delivered')
                    ]));
            }
        }

        $existingCancellation = $this->OrderCancellations->find()
        ->where(['order_id' => $orderId])
        ->where(['order_item_id' => $data['order_item_id']])
        ->first();

        if ($existingCancellation) {
            $messages = [
                'Pending' => __('Cancellation has already been initiated'),
                'Approved' => __('Cancellation is already in progress'),
                'Completed' => __('This order has already been cancelled')
            ];

            return $this->response->withType('application/json')->withStatus(400)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 400,
                    'message' => $messages[$existingCancellation->status] ?? __('Order cancellation issue')
                ]));
        }


        $cancellationData = [
            'order_id' => $orderId,
            'customer_id' => $customerId, // Ensure customer_id is included
            'order_item_id' => $data['order_item_id'],
            'order_cancellation_category_id' => $data['reason_id'],
            'reason' => $data['reason'] ?? null,
            'status' => 'Pending',
            'canceled_at' => date('Y-m-d H:i:s')
        ];

        $order->status = 'Pending Cancellation';
        if (!$this->Orders->save($order)) {
            return $this->response->withType('application/json')->withStatus(500)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 500,
                    'message' => __('Failed to cancel the order')
                ]));
        }

        $cancellation = $this->OrderCancellations->add_record_data($cancellationData);

        if (!$cancellation) {
            return $this->response->withType('application/json')->withStatus(500)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 500,
                    'message' => __('Failed to cancel the order')
                ]));
        }

        $trackingData = [
            'order_id' => $orderId,
            'status' => 'Pending Cancellation',
            'comment' => $data['reason'] ?? null,
        ];
        $this->OrderTrackingHistories->add_record($trackingData);

        $category = $this->OrderCancellationCategories->find()
            ->select(['name'])
            ->where(['id' => $data['reason_id']])
            ->first();

        $reasonName = $category ? $category->name : __('No reason provided');

        $adminEmails = Configure::read('Settings.ADMIN_EMAILS');
        $to = $adminEmails[0];
        $cc = count($adminEmails) > 1 ? array_slice($adminEmails, 1) : null;
        $subject = 'Order Cancellation Request';
        $template = 'order_cancellation';
        $viewVars = [
            'order_number' => $order['order_number'],
            'customer_name' => $identity['first_name'] . ' ' . $identity['last_name'],
            'reason' => $reasonName,
            'comment' => $data['reason'] ?? 'No comment provided',
            'canceled_at' => $cancellationData['canceled_at'],
        ];

       // $sendEmail = $this->Global->send_email($to, null, $subject, $template, $viewVars, null, $cc);
        $sendEmail = $this->WebsiteFunction->orderCancellationRequestEmail($data['order_item_id'], $data['reason_id'], $data['reason'], date('Y-m-d H:i:s'));

        if ($sendEmail) {
            return $this->response->withType('application/json')->withStatus(200)
                ->withStringBody(json_encode([
                    'status' => 'success',
                    'code' => 200,
                    'message' => __('Order cancellation has been initiated successfully, and an email notification has been sent to the admin.')
                ]));
        } else {
            return $this->response->withType('application/json')->withStatus(200)
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'code' => 200,
                    'message' => __('Order cancellation has been initiated successfully, but the email notification could not be sent.')
                ]));
        }
    }





    public function orderReturnWeb()
    {
        if ($this->request->is('post')) {

            $identity = $this->request->getSession()->read('Auth.User');
            $data = $this->request->getData();
            $orderItemId = $data['order_item_id'];
            $customerAddressId = $data['return_customer_address_id'] ?? NULL;
            $returnShowroomId = $data['return_showroom_id'] ?? NULL;
            $order_return_category_id = $data['order_return_category_id'] ?? NULL;
            $requestType = !empty($data['request_type']) ? $data['request_type'] : 'Return';



            if (!$identity) {
                return $this->response->withType('application/json')->withStatus(401)
                ->withStringBody(json_encode([
                    'status' => __('error'),
                    'code' => 401,
                    'message' => __('User is not authenticated')
                ]));
            }

			$users = $this->Users->find()
			->contain([
				'Customers' => function ($q) {
					return $q->select(['id']); // Select only the Customer.id field
				}
			])
			->select(['Users.id']) // Select the necessary fields from Users
			->where(['Users.status' => 'A'])
			->where(['Users.id' => $identity->id])
			->first();

			$customerId = $users->customer->id;

            $userId = $identity['id'];

            $user = $this->Users->get($userId);

            if (empty($orderItemId)) {
				 return $this->response->withType('application/json')->withStatus(400)
                ->withStringBody(json_encode([
                    'status' => __('error'),
                    'code' => 400,
                    'message' => __('Order item ID is required')
                ]));
            }

            try {

                $orderItem = $this->OrderItems->get($orderItemId, [
                    'contain' => ['Products']
                ]);

            } catch (\Cake\Datasource\Exception\RecordNotFoundException $e) {

				 return $this->response->withType('application/json')->withStatus(400)
                ->withStringBody(json_encode([
                    'status' => __('error'),
                    'code' => 400,
                    'message' => __('Order item not found')
                ]));
            }

            $order = $this->Orders->get($orderItem['order_id']);

            if ($order->customer_id !== $customerId) {

				 return $this->response->withType('application/json')->withStatus(400)
                ->withStringBody(json_encode([
                    'status' => __('error'),
                    'code' => 400,
                    'message' => __('Unauthorized to return this order')
                ]));
            }

            if ($order->payment_method == 'Credit') {

				 return $this->response->withType('application/json')->withStatus(400)
                ->withStringBody(json_encode([
                    'status' => __('error'),
                    'code' => 400,
                    'message' => __('The order cannot be canceled as it was made on credit')
                ]));
            }

            if ($orderItem['status'] !== 'Delivered' && $requestType == 'Return') {

				 return $this->response->withType('application/json')->withStatus(400)
                ->withStringBody(json_encode([
                    'status' => __('error'),
                    'code' => 400,
                    'message' => __('Only delivered items can be returned')
                ]));
            }

            $existingReturn = $this->OrderReturns->find()
                ->where(['order_item_id' => $orderItemId])
                ->first();

            if ($existingReturn) {
                switch ($existingReturn->status) {
                    case 'Pending':

						 return $this->response->withType('application/json')->withStatus(400)
						->withStringBody(json_encode([
                            'status' => __('error'),
                            'code' => 400,
                            'message' => __('A return request has already been initiated and is awaiting approval.')
                        ]));

                    case 'Approved':

						return $this->response->withType('application/json')->withStatus(400)
						->withStringBody(json_encode([
                            'status' => __('error'),
                            'code' => 400,
                            'message' => __('A return request has already been approved and is in progress.')
                        ]));

                    case 'Completed':

						return $this->response->withType('application/json')->withStatus(400)
						->withStringBody(json_encode([
                            'status' => __('error'),
                            'code' => 400,
                            'message' => __('This order item has already been returned.')
                        ]));

                    default:

						return $this->response->withType('application/json')->withStatus(400)
						->withStringBody(json_encode([
                            'status' => __('error'),
                            'code' => 400,
                            'message' => __('This order item has an existing return request with status: ' . $existingReturn->status)
                        ]));
                }
            }

            $returnData = [
                'customer_address_id' => $customerAddressId,
                'request_type' => $requestType,
                'return_to' => !empty($returnShowroomId) ? 'Showroom' : null,
                'return_to_id' => $returnShowroomId,
                'order_id' => $orderItem['order_id'],
                'customer_id' => $customerId,
                'order_item_id' => $orderItemId,
                'order_return_category_id' => $order_return_category_id ?? null,
                'reason' => $data['reason'] ?? null,
                'status' => 'Pending',
                'requested_at' => date('Y-m-d H:i:s'),
                'return_amount' => $orderItem['price'] *  $orderItem['quantity']
            ];

            $orderItem->status = 'Pending Return';

            if ($this->OrderItems->save($orderItem)) {

                $returnId = $this->OrderReturns->add_record($returnData);

                if ($returnId) {

                    $files = $data['images'] ?? [];
                    $uploadError = false;

                    if (!empty($files)) {
                        foreach ($files as $file) {
                            if ($file->getError() === UPLOAD_ERR_OK) {
                                $fileName = trim($file->getClientFilename());

                                if (!empty($fileName)) {
                                    $imageTmpName = $file->getStream()->getMetadata('uri');

                                    $rand = strtoupper(substr(uniqid(sha1((string)time())), -5));
                                    $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                                    $filePath = Configure::read('Settings.PRODUCT_RETURN');
                                    $folderPath = $uploadFolder . $filePath;
                                    $targetDir = WWW_ROOT . $folderPath;
                                    $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                                    $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                                    $uploadResult = $this->Media->upload($imageTmpName, $targetDir, $imageFile, $folderPath);
                                    if ($uploadResult === 'Success') {
                                        $imageData = [
                                            'order_return_id' => $returnId,
                                            'image_url' => $folderPath . $imageFile
                                        ];

                                        if (!$this->OrderReturnImages->add_record($imageData)) {
                                            $uploadError = true;
                                        }
                                    } else {
                                        $uploadError = true;
                                    }
                                }
                            }
                        }
                    }

                    $trackingData = [
                        'order_item_id' => $orderItemId,
                        'status' => 'Pending Return',
                        'comment' => $data['reason'] ?? null,
                    ];
                    $tracking = $this->OrderTrackingHistories->newEntity($trackingData);
                    $this->OrderTrackingHistories->save($tracking);


                    $category = $this->OrderReturnCategories->find()
                        ->select(['name'])
                        ->where(['id' => $data['order_return_category_id']])
                        ->first();

                    $reasonName = $category ? $category->name : __('No reason provided');

                    $adminEmails = Configure::read('Settings.ADMIN_EMAILS');
                    $to = $adminEmails[0];
                    $cc = count($adminEmails) > 1 ? array_slice($adminEmails, 1) : null;
                    $subject = 'Order Item Return Request';
                    $template = 'order_item_return';
                    $viewVars = [
                        'order_number' => $order['order_number'],
                        'order_item_product' => $orderItem['product']['name'],
                        'customer_name' => $user['first_name'] . ' ' . $user['last_name'],
                        'reason' => $reasonName,
                        'comment' => $data['reason'] ?? 'No comment provided',
                        'requested_at' => $returnData['requested_at'],
                    ];

                    // $sendEmail = $this->Global->send_email($to, null, $subject, $template, $viewVars, null, $cc);
                    $sendEmail = $this->WebsiteFunction->orderReturnRequestEmail($orderItemId, $data['order_return_category_id'], $data['reason'], date('Y-m-d H:i:s'));

                    if ($sendEmail) {

							return $this->response->withType('application/json')->withStatus(200)
						->withStringBody(json_encode([
                            'status' => __('success'),
                            'code' => 200,
                            'message' => __('Order item return has been initiated successfully, and an email notification has been sent to the admin.')
                        ]));
                    } else {
							return $this->response->withType('application/json')->withStatus(400)
						->withStringBody(json_encode([
                            'status' => __('error'),
                            'code' => 400,
                            'message' => __('Order item return has been initiated successfully, but the email notification could not be sent to the admin.')
                        ]));
                    }

                } else {

					return $this->response->withType('application/json')->withStatus(400)
						->withStringBody(json_encode([
                        'status' => __('error'),
                        'code' => 400,
                        'message' => __('Failed to process the return')
                    ]));
                }
            } else {

				return $this->response->withType('application/json')->withStatus(400)
						->withStringBody(json_encode([
                    'status' => __('error'),
                    'code' => 400,
                    'message' => __('Failed to cancel the order')
                ]));
            }
        } else {

				return $this->response->withType('application/json')->withStatus(405)
						->withStringBody(json_encode([
                'status' => __('error'),
                'code' => 405,
                'message' => __('Method not allowed')
            ]));
        }


    }
    public function buyNow()
    {
        $this->response = $this->response->withType('application/json');

        if (!$this->request->is('post')) {
            $result = ['status' => __('error'), 'message' => __('Method Not Allowed')];
            return $this->response->withStringBody(json_encode($result));
        }

        if (empty($this->request->getSession()->read('Auth.User'))) {
            $result = [
                'status' => __('error'),
                'code' => 400,
                'message' => __('Login required to access buy now.')
            ];
            return $this->response->withStringBody(json_encode($result));
        }

        $data = $this->request->getData();
        $guestToken = $this->request->getSession()->read('cartId') ?? null;
        $identity = $this->request->getSession()->read('Auth.User');

        // Validate required fields
        if (empty($data['product_id'])) {
            $result = [
                'status' => __('error'),
                'code' => 400,
                'message' => __('Product ID is required')
            ];
            return $this->response->withStringBody(json_encode($result));
        }

        // Set default quantity if not provided
        if (empty($data['quantity']) || $data['quantity'] == "NaN") {
            $data['quantity'] = 1;
        }

        // Generate Guest Token if not logged in and no guest-token exists
        if (!$identity && !$guestToken) {
            $guestToken = Text::uuid();
            $this->request->getSession()->write('cartId', $guestToken);
        }

        // Determine Customer ID or Use Guest Token
        $customerId = $identity ? $identity->id : null;
        if (!empty($customerId)) {
            $users = $this->Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A'])
                ->where(['Users.id' => $customerId])
                ->first();
            $customerId = $users->customer->id;
        }

        // Find or Create Cart
        $cartConditions = $customerId
            ? ['customer_id' => $customerId]
            : ['guest_token' => $guestToken];

        $cart = $this->Carts->find()->where($cartConditions)->first();
        if (!$cart) {
            $cart = $this->Carts->newEntity([
                'customer_id' => $customerId,
                'guest_token' => $guestToken,
            ]);
            if (!$this->Carts->save($cart)) {
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Failed to create cart')
                ];
                return $this->response->withStringBody(json_encode($result));
            }
        }

        // Clear existing cart items
        $connection = $this->CartItems->getConnection();
        $connection->begin();

        try {
            // Delete all existing cart items
            $existingItems = $this->CartItems->find()
                ->where(['cart_id' => $cart->id])
                ->toArray();

            foreach ($existingItems as $item) {
                $this->CartItems->delete($item);
            }

            // Add the new item to cart
            $productVariantId = !empty($data['variant_id']) ? $data['variant_id'] : null;
            $productAttributeId = !empty($data['product_attribute_id']) ? $data['product_attribute_id'] : null;

            // Add a new cart item
            $newItemData = [
                'cart_id' => intval($cart->id),
                'product_id' => intval($data['product_id']),
                'quantity' => intval($data['quantity']),
            ];

            if (!empty($productVariantId)) {
                $newItemData['product_variant_id'] = $productVariantId;
            }

            if (!empty($productAttributeId)) {
                $newItemData['product_attribute_id'] = $productAttributeId;
            }

            $newItem = $this->CartItems->newEntity($newItemData);

            if (!$this->CartItems->save($newItem)) {
                $connection->rollback();
                $result = [
                    'status' => __('error'),
                    'code' => 200,
                    'message' => __('Failed to add cart item.'),
                    'data' => $newItemData
                ];
                return $this->response->withStringBody(json_encode($result));
            }

            // Set price for the cart item
            $price = null;
            if (isset($productVariantId)) {
                $productVariant = $this->CartItems->ProductVariants->find()
                    ->select(['promotion_price'])
                    ->where(['id' => $productVariantId])
                    ->first();

                $price = $productVariant ? $productVariant->promotion_price : null;
            }

            if (!$price) {
                $product = $this->CartItems->Products->find()
                    ->select(['promotion_price'])
                    ->where(['id' => $data['product_id']])
                    ->first();

                $price = $product ? $product->promotion_price : null;
            }

            if ($price !== null) {
                $newItem->price = $price;
                $newItem->total_price = number_format($newItem->quantity * $price, 2, '.', '');
                $this->CartItems->save($newItem);
            }

            $connection->commit();

            // Redirect to checkout page
            $result = [
                'status' => __('success'),
                'code' => 200,
                'message' => __('Ready to checkout'),
                'redirect' => '/account/checkout'
            ];

            return $this->response->withStringBody(json_encode($result));

        } catch (\Exception $e) {
            $connection->rollback();
            $result = [
                'status' => __('error'),
                'code' => 500,
                'message' => __('An error occurred: ') . $e->getMessage()
            ];
            return $this->response->withStringBody(json_encode($result));
        }
    }



    /**
     * Send order confirmation email
     *
     * @param int|null $orderId Order ID
     * @return \Cake\Http\Response|null
     */
    public function orderemail()
    {
        // $data['reason_id'] = $resionId ?? 1;
        // $data['reason'] = $reason ?? "No comment provided";
        // $cancellationData['canceled_at'] = $returnDate ?? date('Y-m-d H:i:s');

         $data2 = $this->WebsiteFunction->userAccountDeleteEmail('<EMAIL>', 'Axit Kumar');
         dd($data2);


        $adminEmails = Configure::read('Settings.ADMIN_EMAILS');
        $to = "<EMAIL>"; // $adminEmails[0];
        $cc = count($adminEmails) > 1 ? array_slice($adminEmails, 1) : null;
        $subject = 'Account Deletion Request';
        $template = 'delete_customer_account_email';
        $viewVars = [
            'site_url' => Configure::read('Settings.SITE_URL'),
            'logo_url' => Configure::read('Settings.PG_ENVIRONMENT') === 'sandbox'
                ? 'https://babiken.com360degree.com/assets/logo.png'
                : Configure::read('Settings.SITE_URL') . '/assets/logo.png',
            'customer_name' => 'Axit Kumar',  //$identity['first_name'] . ' ' . $identity['last_name']
            'email' => '<EMAIL>',
        ];

        $sendEmail = $this->Global->send_email($to, null, $subject, $template, $viewVars, null, $cc);
        dd($sendEmail);
        return false;
    }




}
