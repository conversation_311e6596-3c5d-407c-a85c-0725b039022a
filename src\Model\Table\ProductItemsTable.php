<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\Log\Log;


class ProductItemsTable extends Table
{
    public function initialize(array $config): void
    {
        parent::initialize($config);


        $this->belongsTo('Products', [
            'foreignKey' => 'product_id',
        ]);

        $this->belongsTo('ProductVariants', [
            'foreignKey' => 'varriant_id',
        ]);
    }

    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->integer('product_id')
            ->requirePresence('product_id', 'create')
            ->notEmptyString('product_id');

        $validator
            ->scalar('unique_id')
            ->maxLength('unique_id', 255)
            ->notEmptyString('unique_id');

        $validator
            ->scalar('barcode')
            ->maxLength('barcode', 255)
            ->notEmptyString('barcode');

        return $validator;
    }

    //Get item by stock_movement_item_id
    public function getByStockMovemenetItemId($stockMovementItemId, $first_product_count, $quantity)
    {

         Log::debug("Item List Requested");
        Log::debug("Item ID: ".$stockMovementItemId." first product count: ".$first_product_count." quantity: ".$quantity);
       
    

        if($first_product_count == null || $first_product_count <= 0) {
            $result = $this->find() 
        ->where(['stock_movement_item_id' => $stockMovementItemId])->orderAsc('first_product_count') // Optional: for predictable order
        ->limit((int)$quantity)
        ->toArray();
            Log::debug("Result Count: " . count($result));
        Log::debug("Result: " . json_encode($result));
return $result;

        //     return $this->find()
        // ->where(['stock_movement_item_id' => $stockMovementItemId])
        // ->toArray();
        }
        $result = $this->find()
        ->where([
            'stock_movement_item_id' => $stockMovementItemId,
            'first_product_count >=' => $first_product_count
        ])
        ->orderAsc('first_product_count') // Optional: for predictable order
        ->limit((int)$quantity)
        ->toArray();
        return $result;
        Log::debug("Query");
        Log::debug(print_r($result, true));
    }


    public function getLastBarcodeForItem($stockMovementItemId, $productId)
    {
        return $this->find()
            ->where([
                'stock_movement_item_id' => $stockMovementItemId,
                'product_id' => $productId
            ])
            ->toArray();
    }

    //Update item status based on unique_id
    public function updateStatusByUniqueId($uniqueId, $newStatus)
    {
        $productItem = $this->find()
            ->where(['unique_id' => $uniqueId])
            ->first();

        if ($productItem) {
            $productItem->status = $newStatus;
            if ($this->save($productItem)) {
                return true;
            } else {
                return false;
            }
        } else {
            return ['success' => false, 'message' => 'Product item not found'];
        }
    }

    
}
