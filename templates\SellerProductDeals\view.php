<?php

/**
 * @var \App\View\AppView $this
 * @var \App\Model\Entity\ProductDeal $productDeal
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<style>
    .deal-info-card {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        border-left: 4px solid #007bff;
    }
    .deal-status {
        font-size: 1.1rem;
        font-weight: 600;
    }
    .status-pending { color: #ffc107; }
    .status-approved { color: #28a745; }
    .status-rejected { color: #dc3545; }
    .status-inactive { color: #6c757d; }
    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #e9ecef;
    }
    .info-row:last-child {
        border-bottom: none;
    }
    .info-label {
        font-weight: 600;
        color: #495057;
        min-width: 150px;
    }
    .info-value {
        color: #212529;
        text-align: right;
    }
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;
    }
    .badge-pending {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }
    .badge-approved {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    .badge-rejected {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    .badge-inactive {
        background-color: #e2e3e5;
        color: #383d41;
        border: 1px solid #d6d8db;
    }
    .action-buttons {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }
    .timeline-item {
        position: relative;
        padding-left: 2rem;
        margin-bottom: 1rem;
    }
    .timeline-item::before {
        content: '';
        position: absolute;
        left: 0.5rem;
        top: 0.5rem;
        width: 0.75rem;
        height: 0.75rem;
        border-radius: 50%;
        background-color: #007bff;
    }
    .timeline-item::after {
        content: '';
        position: absolute;
        left: 0.875rem;
        top: 1.25rem;
        width: 2px;
        height: calc(100% + 0.5rem);
        background-color: #dee2e6;
    }
    .timeline-item:last-child::after {
        display: none;
    }
</style>
<?php $this->end(); ?>

<div class="section-header d-flex justify-content-between align-items-center mb-3">
    <ul class="breadcrumb breadcrumb-style mb-0">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'sellerDashboard']) ?>">
                <h4 class="page-title m-b-0"><?= __('Seller Dashboard') ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'SellerProductDeals', 'action' => 'index']) ?>">
                <?= __('Deals of the Day') ?>
            </a>
        </li>
        <li class="breadcrumb-item active">
            <?= __('View Deal') ?>
        </li>
    </ul>
    <a href="javascript:void(0);" class="d-flex align-items-center" id="back-button-mo" onclick="history.back();">
        <span class="rotate me-2">➥</span><small style="font-weight: bold"><?= __('BACK') ?></small>
    </a>
</div>

<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>

<div class="section-body">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4><?= __('Deal Details') ?></h4>
                <div class="actions">
                    <?php if ($productDeal->status !== 'A'): ?>
                        <a href="<?= $this->Url->build(['controller' => 'SellerProductDeals', 'action' => 'edit', $productDeal->id]) ?>" 
                           class="btn btn-primary me-2">
                            <i class="fas fa-edit"></i> <?= __('Edit') ?>
                        </a>
                    <?php endif; ?>
                    <a href="<?= $this->Url->build(['controller' => 'SellerProductDeals', 'action' => 'index']) ?>" 
                       class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> <?= __('Back to List') ?>
                    </a>
                </div>
            </div>
            
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <!-- Product Information -->
                        <div class="deal-info-card">
                            <h5 class="mb-3"><i class="fas fa-box me-2"></i><?= __('Product Information') ?></h5>
                            <div class="info-row">
                                <span class="info-label"><?= __('Product Name') ?></span>
                                <span class="info-value"><?= h($productDeal->product->name ?? 'N/A') ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label"><?= __('Product ID') ?></span>
                                <span class="info-value">#<?= h($productDeal->product->id ?? 'N/A') ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label"><?= __('Product Status') ?></span>
                                <span class="info-value">
                                    <span class="badge badge-success"><?= __('Active') ?></span>
                                </span>
                            </div>
                        </div>

                        <!-- Deal Information -->
                        <div class="deal-info-card">
                            <h5 class="mb-3"><i class="fas fa-tag me-2"></i><?= __('Deal Information') ?></h5>
                            <div class="info-row">
                                <span class="info-label"><?= __('Deal ID') ?></span>
                                <span class="info-value">#<?= h($productDeal->id) ?></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label"><?= __('Offer Price') ?></span>
                                <span class="info-value">
                                    <strong class="text-success">
                                        <?= h($currencySymbol . number_format((float)$productDeal->offer_price, 2, $decimalSeparator, $thousandSeparator)) ?>
                                    </strong>
                                </span>
                            </div>
                            <div class="info-row">
                                <span class="info-label"><?= __('Start Date') ?></span>
                                <span class="info-value">
                                    <i class="fas fa-calendar-alt me-1"></i>
                                    <?= h($productDeal->start_date ? $productDeal->start_date->format($dateFormat) : 'N/A') ?>
                                </span>
                            </div>
                            <div class="info-row">
                                <span class="info-label"><?= __('End Date') ?></span>
                                <span class="info-value">
                                    <i class="fas fa-calendar-alt me-1"></i>
                                    <?= h($productDeal->end_date ? $productDeal->end_date->format($dateFormat) : 'N/A') ?>
                                </span>
                            </div>
                            <div class="info-row">
                                <span class="info-label"><?= __('Duration') ?></span>
                                <span class="info-value">
                                    <?php
                                    if ($productDeal->start_date && $productDeal->end_date) {
                                        $duration = $productDeal->start_date->diffInDays($productDeal->end_date);
                                        echo h($duration . ' ' . __('days'));
                                    } else {
                                        echo __('N/A');
                                    }
                                    ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <!-- Status Information -->
                        <div class="deal-info-card">
                            <h5 class="mb-3"><i class="fas fa-info-circle me-2"></i><?= __('Status Information') ?></h5>
                            <div class="info-row">
                                <span class="info-label"><?= __('Current Status') ?></span>
                                <span class="info-value">
                                    <?php
                                    $statusInfo = $statusMap[$productDeal->status] ?? ['label' => 'Unknown', 'class' => 'label-danger'];
                                    $badgeClass = 'badge-' . strtolower(str_replace(' ', '', $statusInfo['label']));
                                    ?>
                                    <span class="status-badge <?= h($badgeClass) ?>">
                                        <?= h($statusInfo['label']) ?>
                                    </span>
                                </span>
                            </div>
                            
                            <?php if ($productDeal->status === 'P'): ?>
                                <div class="alert alert-warning mt-3">
                                    <i class="fas fa-clock me-2"></i>
                                    <strong><?= __('Pending Approval') ?></strong><br>
                                    <small><?= __('Your deal is currently under review by our admin team. You will be notified once it is approved or rejected.') ?></small>
                                </div>
                            <?php elseif ($productDeal->status === 'A'): ?>
                                <div class="alert alert-success mt-3">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <strong><?= __('Approved & Live') ?></strong><br>
                                    <small><?= __('Your deal has been approved and is now live on the platform.') ?></small>
                                </div>
                            <?php elseif ($productDeal->status === 'I'): ?>
                                <div class="alert alert-danger mt-3">
                                    <i class="fas fa-times-circle me-2"></i>
                                    <strong><?= __('Your deal has been rejected.') ?></strong><br>
                                    <?php if (!empty($productDeal->rejection_reason)): ?>
                                        <strong>Reason: </strong><span class="text-white "><i><?= h($productDeal->rejection_reason) ?></i></span>
                                    <?php else: ?>
                                        <strong><small><?= __(' Please contact admin for more information.') ?></small></strong>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Timeline -->
                        <!-- <div class="deal-info-card">
                            <h5 class="mb-3"><i class="fas fa-history me-2"></i><?= __('Timeline') ?></h5>
                            <div class="timeline-item">
                                <strong><?= __('Deal Created') ?></strong><br>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    <?= h($productDeal->created ? $productDeal->created->format($dateFormat . ' H:i') : 'N/A') ?>
                                </small>
                            </div>
                            <div class="timeline-item">
                                <strong><?= __('Last Modified') ?></strong><br>
                                <small class="text-muted">
                                    <i class="fas fa-edit me-1"></i>
                                    <?= h($productDeal->modified ? $productDeal->modified->format($dateFormat . ' H:i') : 'N/A') ?>
                                </small>
                            </div>
                            <?php if ($productDeal->status === 'A'): ?>
                                <div class="timeline-item">
                                    <strong><?= __('Deal Approved') ?></strong><br>
                                    <small class="text-muted">
                                        <i class="fas fa-check me-1"></i>
                                        <?= h($productDeal->modified ? $productDeal->modified->format($dateFormat . ' H:i') : 'N/A') ?>
                                    </small>
                                </div>
                            <?php endif; ?>
                        </div> -->

                        <!-- Quick Actions -->
                        <!-- <div class="deal-info-card">
                            <h5 class="mb-3"><i class="fas fa-bolt me-2"></i><?= __('Quick Actions') ?></h5>
                            <div class="action-buttons">
                                <?php if ($productDeal->status !== 'A'): ?>
                                    <a href="<?= $this->Url->build(['controller' => 'SellerProductDeals', 'action' => 'edit', $productDeal->id]) ?>" 
                                       class="btn btn-warning btn-sm">
                                        <i class="fas fa-edit"></i> <?= __('Edit') ?>
                                    </a>
                                <?php endif; ?>
                                <a href="<?= $this->Url->build(['controller' => 'SellerProductDeals', 'action' => 'index']) ?>" 
                                   class="btn btn-secondary btn-sm">
                                    <i class="fas fa-list"></i> <?= __('All Deals') ?>
                                </a>
                                <a href="<?= $this->Url->build(['controller' => 'SellerProductDeals', 'action' => 'add']) ?>" 
                                   class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i> <?= __('New Deal') ?>
                                </a>
                            </div>
                        </div> -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js') ?>"></script>
<?php $this->end(); ?>
