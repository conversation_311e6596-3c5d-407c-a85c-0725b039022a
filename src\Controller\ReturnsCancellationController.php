<?php
declare(strict_types=1);

namespace App\Controller;
use Cake\Database\Expression\IdentifierExpression;
use Cake\Core\Configure;
use Cake\ORM\Expression\QueryExpression;

/**
 * Zones Controller
 *
 * @property \App\Model\Table\ZonesTable $zones
 */
class ReturnsCancellationController extends AppController
{
    /**
     * Index method
     *
     * @return \Cake\Http\Response|null|void Renders view
     */

    protected $Orders;
    protected $OrderItems;
    protected $Products;
    protected $Roles;
    protected $ProductAttributes;
    protected $OrderReturns;
    protected $OrderReturnCategories;
    protected $Warehouses;
    protected $Showrooms;
    protected $Shipments;
    protected $ShipmentOrders;
    protected $ShipmentOrderItems;
    protected $CustomerAddresses;
    protected $ZoneMunicipalities;
    protected $Municipalities;
    protected $Cities;
    protected $ProductStocks;
    protected $OrderTrackingHistories;
    protected $Loyalty;

    public function initialize(): void
    {
        parent::initialize();
        $this->viewBuilder()->setLayout('admin');
        $this->loadComponent('Global');
        $this->loadComponent('Media');

        $this->Orders = $this->fetchTable('Orders');
        $this->OrderItems = $this->fetchTable('OrderItems');
        $this->Products = $this->fetchTable('Products');
        $this->Roles = $this->fetchTable('Roles');
        $this->ProductAttributes = $this->fetchTable('ProductAttributes');
        $this->OrderReturns = $this->fetchTable('OrderReturns');
        $this->OrderReturnCategories = $this->fetchTable('OrderReturnCategories');
        $this->Warehouses = $this->fetchTable('Warehouses');
        $this->Showrooms = $this->fetchTable('Showrooms');
        $this->Shipments = $this->fetchTable('Shipments');
        $this->ShipmentOrders = $this->fetchTable('ShipmentOrders');
        $this->ShipmentOrderItems = $this->fetchTable('ShipmentOrderItems');
        $this->CustomerAddresses = $this->fetchTable('CustomerAddresses');
        $this->ZoneMunicipalities = $this->fetchTable('ZoneMunicipalities');
        $this->Municipalities = $this->fetchTable('Municipalities');
        $this->Cities = $this->fetchTable('Cities');
        $this->ProductStocks = $this->fetchTable('ProductStocks');
        $this->OrderTrackingHistories = $this->fetchTable('OrderTrackingHistories');
        $this->Loyalty = $this->fetchTable('Loyalty');
    }

    public function index()
    {

    }

    public function add()
    {
        $returnRequest = $this->OrderReturns->newEmptyEntity();

        if ($this->request->is('post')) {

            $data = $this->request->getData();

            $orderId = $data['order_id'];
            $pickupRequired = $data['pickup_required'] ?? 'No';
            $pickupCharge = $data['pickup_charge'] ?? 0;
            $note = $data['note'] ?? null;
            $return_to = ($pickupRequired === 'Yes') ? $data['return_to'] : null;
            $return_to_id = ($pickupRequired === 'Yes') ? $data['return_to_id'] : null;
            $orderItemIds = $data['order_item_id'] ?? [];
            $prices = $data['price'] ?? [];
            $returnQuantities = $data['return_quantity'] ?? [];
            $returnReasons = $data['return_reason'] ?? [];
            $returnProductImages = $data['return_product_image'] ?? []; 
            $customerAddressId = $data['customer_address_id'] ?? null;

            // Get logged-in user ID
            $user = $this->Authentication->getIdentity();
            $requestedBy = $user->id;

            // If no customer_address_id is provided, insert a new address
            if (empty($customerAddressId)) {
                // Get customer_id from Orders table
                $order = $this->Orders->get($orderId);
                $customerId = $order->customer_id;

                // Prepare address data (adjust field names as per your form)
                $addressData = [
                    'customer_id' => $customerId,
                    'name' => $data['name'] ?? null,
                    'title' => $data['title'] ?? null,
                    'country_code1' => $data['country_code1'] ?? null,
                    'phone_no1' => $data['phone_no1'] ?? null,
                    'country_code2' => $data['country_code2'] ?? null,
                    'phone_no2' => $data['phone_no2'] ?? null,
                    'city_id' => $data['city_id'] ?? null,
                    'municipality_id' => $data['municipality_id'] ?? null,
                    'address_line1' => $data['address_line1'] ?? null,
                    'house_no' => $data['house_no'] ?? null,
                    'landmark' => $data['landmark'] ?? null,
                    'zipcode' => $data['zipcode'] ?? null,
                    // Add other fields if any
                ];

                $customerAddress = $this->CustomerAddresses->newEntity($addressData);

                if ($this->CustomerAddresses->save($customerAddress)) {
                    $customerAddressId = $customerAddress->id;
                } else {
                    $this->Flash->error(__('Failed to save new customer address.'));
                    return $this->redirect(['action' => 'add']);
                }
            }

            for ($i = 0; $i < count($orderItemIds); $i++) {
                $uploadedImages = [];

                // Handle image uploads (if any)
                if (!empty($returnProductImages[$orderItemIds[$i]])) {
                    foreach ($returnProductImages[$orderItemIds[$i]] as $image) {

                        if ($image instanceof \Laminas\Diactoros\UploadedFile && $image->getError() === UPLOAD_ERR_OK) {
                            $fileName = $image->getClientFilename();
                            $imageTmpName = $image->getStream()->getMetadata('uri');
                            $rand = strtoupper(substr(uniqid(sha1((string) time())), -5));

                            $uploadFolder = Configure::read('Settings.UPLOAD_FOLDER');
                            $filePath = Configure::read('Constants.ORDER_RETURN_DEFECT_IMAGE');
                            $folderPath = $uploadFolder . $filePath;
                            $targetDir = WWW_ROOT . $folderPath;

                            $ext = pathinfo($fileName, PATHINFO_EXTENSION);
                            $imageFile = pathinfo($fileName, PATHINFO_FILENAME) . '_' . $rand . '.' . $ext;

                            $uploadResult = $this->Media->upload($imageTmpName, $targetDir, $imageFile, $folderPath);

                            if ($uploadResult !== 'Success') {
                                $this->Flash->error(__('Image upload failed for item ID: ') . $orderItemIds[$i]);
                                return $this->redirect(['action' => 'add']);
                            } else {
                                $uploadedImages[] = $folderPath . $imageFile;
                            }
                        }
                    }
                }

                $returnImage = !empty($uploadedImages) ? implode(',', $uploadedImages) : null;

                $returnAmount = (float)$returnQuantities[$i] * (float)$prices[$i];

                $orderReturnData = [
                    'order_id' => $orderId,
                    'order_item_id' => $orderItemIds[$i],
                    'order_return_category_id' => $returnReasons[$i],
                    'return_quantity' => $returnQuantities[$i],
                    'return_product_image' => $returnImage,
                    'status' => 'Pending',
                    'return_to' => $return_to,
                    'return_to_id' => $return_to_id,
                    'pickup_required' => $pickupRequired,
                    'pickup_charge' => $pickupCharge,
                    'note' => $note,
                    'requested_by' => $requestedBy,
                    'requested_at' => date('Y-m-d H:i:s'),
                    'return_amount' => $returnAmount,
                    'customer_address_id' => $customerAddressId,
                ];

                $orderReturn = $this->OrderReturns->newEntity($orderReturnData);

                if (!$this->OrderReturns->save($orderReturn)) {
                    $this->Flash->error(__('Failed to save return entry for order item ID: ') . $orderItemIds[$i]);
                    return $this->redirect(['action' => 'add']);
                } else {
                    
                    $orderItem = $this->OrderItems->get($orderItemIds[$i]);
                    $orderItem->status = 'Pending Return';
                    $this->OrderItems->save($orderItem);

                    $order = $this->Orders->get($orderId);
                    $order->status = 'Pending Return';
                    $this->Orders->save($order);

                    $orderItem = $this->OrderItems->get($orderItemIds[$i], [
                        'contain' => ['Products', 'ProductVariants']
                    ]);
                    $order = $this->Orders->get($orderId, [
                        'contain' => ['Customers.Users']
                    ]);

                    // Build full product name with variant + attributes
                    $fullProductName = $orderItem->product->name ?? 'N/A';
                    $extraDetails = [];

                    if (!empty($orderItem->product_variant->variant_name)) {
                        $extraDetails[] = $orderItem->product_variant->variant_name;
                    }
                    if (!empty($orderItem->product_variant->attributes)) {
                        $extraDetails[] = $orderItem->product_variant->attributes;
                    }

                    if (!empty($extraDetails)) {
                        $fullProductName .= ' (' . implode(', ', $extraDetails) . ')';
                    }

                    // -------------------------------
                    // 🔹 Send Email to Customer
                    // -------------------------------
                    if (!empty($order->customer->user->email)) {
                        $customerName = trim($order->customer->user->first_name . ' ' . $order->customer->user->last_name);
                        $greeting = __('Dear {0},', $customerName);

                        $emailData = [
                            'greeting'      => $greeting,
                            'message'       => __("Your return pickup request has been submitted successfully and is pending processing."),
                            'request_id'    => $orderReturn->id,
                            'requested_date'=> date('Y-m-d'),
                            'order_id'      => $order->id,
                            'order_number'  => $order->order_number ?? $order->id,
                            'order_date'    => $order->order_date ? $order->order_date->format('Y-m-d') : 'N/A',
                            'product_name'  => $fullProductName,
                            'sku'           => !empty($orderItem->product_variant_id)
                                                ? ($orderItem->product_variant->sku ?? 'N/A')
                                                : ($orderItem->product->sku ?? 'N/A'),
                            'status'        => __('Pending Return'),
                        ];

                        $subject = __("Return Pickup Request Submitted - #{$orderReturn->id}");

                        $this->Global->send_email(
                            [$order->customer->user->email], // to customer
                            null,
                            $subject,
                            'return_request_customer',
                            $emailData
                        );
                    }

                    // -------------------------------
                    // 🔹 Send Push Notification to Customer
                    // -------------------------------
                    if (!empty($order->customer->fcm_token)) {
                        $tokens = [$order->customer->fcm_token];
                        $title = __('Return Pickup Request Submitted');
                        $body  = __('Your return pickup request has been submitted successfully and is pending processing.');
                        $customData = [
                            'type'        => 'return_request',
                            'action'      => 'submitted',
                            'request_id'  => $orderReturn->id,
                            'order_id'    => $order->id,
                            'order_number'=> $order->order_number ?? $order->id,
                            'status'      => 'Pending Return'
                        ];

                        $this->Global->sendNotification($tokens, $title, $body, $customData);
                    }

                    // -------------------------------
                    // 🔹 Send Email to Warehouse / Showroom Manager
                    // -------------------------------
                    if ($pickupRequired === 'Yes') {
                        $managerEmail = null;
                        $managerName = 'Manager';

                        if ($return_to === 'Warehouse') {
                            $warehouse = $this->Warehouses->get($return_to_id);
                            $managerEmail = $warehouse->email ?? null;
                            $managerName = $warehouse->name ?? 'Warehouse Manager';
                        } elseif ($return_to === 'Showroom') {
                            $showroom = $this->Showrooms->get($return_to_id);
                            $managerEmail = $showroom->email ?? null;
                            $managerName = $showroom->name ?? 'Showroom Manager';
                        }

                        if ($managerEmail) {
                            $emailData = [
                                'greeting'      => __("Dear {0},", $managerName),
                                'message'       => __("A new return pickup request has been submitted. Please arrange pickup."),
                                'request_id'    => $orderReturn->id,
                                'order_id'      => $order->id,
                                'order_number'  => $order->order_number ?? $order->id,
                                'customer_name' => $order->customer->user->first_name . ' ' . $order->customer->user->last_name,
                                'customer_phone'=> $order->customer->phone_no ?? 'N/A',
                                'pickup_address'=> $customerAddressId ? $this->CustomerAddresses->get($customerAddressId)->address_line1 : 'N/A',
                                'requested_date'=> date('Y-m-d'),
                                'product_name'  => $fullProductName,
                                'quantity'      => $orderReturn->return_quantity,
                            ];

                            $subject = __("New Return Pickup Request - Order #{$order->order_number}");

                            $this->Global->send_email(
                                [$managerEmail],
                                null,
                                $subject,
                                'return_request_manager',
                                $emailData
                            );
                        }
                    }

                }
            }

            $this->Flash->success(__('Order return(s) saved successfully.'));
            return $this->redirect(['controller' => 'ReturnsRefunds', 'action' => 'index']);
        }

        $order_ids = [];
        $order_return_categories_ids = [];

        try {
            $orders = $this->Orders->find()
                ->where([
                    'Orders.status' => 'Delivered'
                ])
                ->order(['Orders.created' => 'DESC'])
                ->all();

            if ($orders->isEmpty()) {
                // You can handle the case where no orders are found
                // For example: set a flag or message
                $noOrdersFound = true;
            } else {
                foreach ($orders as $order) {
                    if (!empty($order->id)) {
                        $order_ids[$order->id] = $order->id;
                    }
                }
            }


            $order_return_categories = $this->OrderReturnCategories->find()
                ->order(['OrderReturnCategories.id' => 'ASC'])
                ->all();

            if ($order_return_categories->isEmpty()) {
                // You can handle the case where no orders are found
                // For example: set a flag or message
                $noOrdersFound = true;
            } else {
                foreach ($order_return_categories as $order_return_category) {
                    if (!empty($order_return_category->id)) {
                        $order_return_categories_ids[$order_return_category->id] = $order_return_category->name;
                    }
                }
            }

        } catch (\Exception $e) {
            // Handle exception, for example by setting an error flag or message
            $errorMessage = __('Something went wrong while fetching delivered orders.');
        }

        $warehouses = $this->Warehouses->find()
            ->where(['Warehouses.status' => 'A'])
            ->order(['Warehouses.name' => 'ASC'])
            ->toArray();

        $showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.status' => 'A'])
                    ->order(['Showrooms.name' => 'ASC'])
                    ->toArray();

        // Get Active Cities
        $cities = $this->Cities->find('list', [
            'keyField' => 'id',
            'valueField' => 'city_name'
        ])
        ->order(['city_name' => 'ASC'])
        ->toArray();

        // Get Active Municipalities
        $municipalities = $this->Municipalities->find('list', [
            'keyField' => 'id',
            'valueField' => 'name'
        ])
        ->where(['status' => 'A'])
        ->order(['name' => 'ASC'])
        ->toArray();

        $this->set(compact('order_ids', 'order_return_categories_ids', 'showrooms', 'warehouses', 'cities', 'municipalities'));

    }

    public function getOrderItemsById()
    {
        $this->request->allowMethod(['post']);

        $orderNumber = $this->request->getData('order_number'); // posted `order_id` is actually `order_number`

        if (empty($orderNumber)) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('Order number is required.')
                ]));
        }

        // 🔷 Find the Order by `order_number`
        $order = $this->Orders->find()
            ->where(['Orders.order_number' => $orderNumber])
            ->select(['id', 'customer_id', 'status'])
            ->first();

        if (!$order || in_array($order->status, ['Deleted'])) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('This order is deleted or not found.')
                ]));
        }

        $customerId = $order->customer_id;
        $orderId = $order->id;

        // 🔷 Customer addresses
        $addresses = $this->Orders->CustomerAddresses->find()
            ->where([
                'CustomerAddresses.customer_id' => $customerId,
                'CustomerAddresses.status' => 'A'
            ])
            ->select([
                'CustomerAddresses.id', 'CustomerAddresses.house_no', 'CustomerAddresses.address_line1',
                'CustomerAddresses.address_line2', 'CustomerAddresses.zipcode',
                'CustomerAddresses.city_id', 'CustomerAddresses.municipality_id'
            ])
            ->contain([
                'Cities' => function ($q) {
                    return $q->select(['Cities.id', 'Cities.city_name']);
                },
                'Municipalities' => function ($q) {
                    return $q->select(['Municipalities.id', 'Municipalities.name']);
                }
            ])
            ->all();

        $addressOptions = [];
        foreach ($addresses as $address) {
            $addressParts = [
                h($address->house_no),
                h($address->address_line1),
                h($address->address_line2),
                !empty($address->city) ? h($address->city->city_name) : '',
                !empty($address->municipality) ? h($address->municipality->name) : '',
                h($address->zipcode)
            ];
            $formattedAddress = implode(', ', array_filter($addressParts));
            $addressOptions[$address->id] = $formattedAddress;
        }

        $excludedStatuses = ['Deleted'];

        // 🔷 Fetch OrderItems by order_id
        $orderItems = $this->OrderItems->find()
            ->where([
                'OrderItems.order_id' => $orderId,
                'OrderItems.quantity >' => 0,
                'Orders.status NOT IN' => $excludedStatuses
            ])
            ->contain([
                'Orders' => ['fields' => ['id', 'status']],
                'Products' => function ($q) {
                    return $q->select(['Products.id', 'Products.name', 'Products.sku'])
                        ->contain([
                            'ProductVariants' => function ($q) {
                                return $q->select([
                                    'ProductVariants.product_id',
                                    'ProductVariants.id',
                                    'ProductVariants.sku',
                                    'ProductVariants.variant_name'
                                ]);
                            }
                        ]);
                }
            ])
            ->toArray();

        foreach ($orderItems as $item) {
            $variantDetails = null;

            if ($item->product_variant_id) {
                $filteredVariants = array_filter($item->product->product_variants, function ($variant) use ($item) {
                    return $variant->id == $item->product_variant_id;
                });

                if (!empty($filteredVariants)) {
                    $variant = reset($filteredVariants);
                    $variantDetails = [
                        'variant_name' => $variant->variant_name,
                        'sku' => $variant->sku
                    ];
                }
            }

            $item->product->sku = $variantDetails['sku'] ?? $item->product->sku;

            if ($variantDetails) {
                $item->product->variant_name = $variantDetails['variant_name'];
            }

            if ($item->product_attribute_id) {
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $item->product_attribute_id])
                    ->contain([
                        'Attributes' => ['fields' => ['Attributes.name']],
                        'AttributeValues' => ['fields' => ['AttributeValues.value']]
                    ])
                    ->first();

                $item->product->attributes = $attributes ?: [];
            } else {
                $item->product->attributes = [];
            }
        }

        $response = [
            'status' => 'success',
            'order_products' => $orderItems,
            'address' => $addressOptions
        ];

        return $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
    }

    public function getOrderItemsForReturnById() 
    {
        $this->request->allowMethod(['post']);

        $orderNumber = $this->request->getData('order_number');

        if (empty($orderNumber)) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('Order number is required.')
                ]));
        }

        $order = $this->Orders->find()
            ->where(['Orders.order_number' => $orderNumber])
            ->select(['id', 'customer_id', 'status'])
            ->first();

        if (!$order || in_array($order->status, ['Deleted'])) {
            return $this->response->withType('application/json')
                ->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('This order is deleted or not found.')
                ]));
        }

        $customerId = $order->customer_id;
        $orderId = $order->id;

        // Addresses
        $addresses = $this->Orders->CustomerAddresses->find()
            ->where([
                'CustomerAddresses.customer_id' => $customerId,
                'CustomerAddresses.status' => 'A'
            ])
            ->select([
                'CustomerAddresses.id', 'CustomerAddresses.house_no', 'CustomerAddresses.address_line1',
                'CustomerAddresses.address_line2', 'CustomerAddresses.zipcode',
                'CustomerAddresses.city_id', 'CustomerAddresses.municipality_id'
            ])
            ->contain([
                'Cities' => function ($q) {
                    return $q->select(['Cities.id', 'Cities.city_name']);
                },
                'Municipalities' => function ($q) {
                    return $q->select(['Municipalities.id', 'Municipalities.name']);
                }
            ])
            ->all();

        $addressOptions = [];
        foreach ($addresses as $address) {
            $parts = [
                h($address->house_no),
                h($address->address_line1),
                h($address->address_line2),
                !empty($address->city) ? h($address->city->city_name) : '',
                !empty($address->municipality) ? h($address->municipality->name) : '',
                h($address->zipcode)
            ];
            $addressOptions[$address->id] = implode(', ', array_filter($parts));
        }

        $excludedStatuses = ['Deleted'];

        $orderItems = $this->OrderItems->find()
            ->where([
                'OrderItems.order_id' => $orderId,
                'OrderItems.quantity >' => 0,
                'Orders.status NOT IN' => $excludedStatuses
            ])
            ->contain([
                'Orders' => ['fields' => ['id', 'status']],
                'Products' => function ($q) {
                    return $q->select(['Products.id', 'Products.name', 'Products.sku'])
                        ->contain([
                            'ProductVariants' => function ($q) {
                                return $q->select([
                                    'ProductVariants.product_id',
                                    'ProductVariants.id',
                                    'ProductVariants.sku',
                                    'ProductVariants.variant_name'
                                ]);
                            }
                        ]);
                }
            ])
            ->toArray();

        $filteredItems = [];

        foreach ($orderItems as $item) {
            // Get total returned quantity for this item
            $returnedQty = $this->OrderReturns->find()
                ->where([
                    'OrderReturns.order_item_id' => $item->id,
                    'OrderReturns.status IN' => ['Pending', 'Approved', 'Refund Pending', 'Refunded'],
                    'OrderReturns.request_type IN' => ['Return', 'Cancellation']
                ])
                ->select(['total_returned' => 'SUM(return_quantity)'])
                ->first()
                ->total_returned ?? 0;

            $remainingQty = $item->quantity - $returnedQty;

            if ($remainingQty <= 0) {
                continue; // Skip if nothing left to return
            }

            // Override quantity with remaining quantity for return
            $item->quantity = $remainingQty;


            // SKU & variant handling
            $variantDetails = null;

            if ($item->product_variant_id) {
                $filteredVariants = array_filter($item->product->product_variants, function ($variant) use ($item) {
                    return $variant->id == $item->product_variant_id;
                });

                if (!empty($filteredVariants)) {
                    $variant = reset($filteredVariants);
                    $variantDetails = [
                        'variant_name' => $variant->variant_name,
                        'sku' => $variant->sku
                    ];
                }
            }

            $item->product->sku = $variantDetails['sku'] ?? $item->product->sku;

            if ($variantDetails) {
                $item->product->variant_name = $variantDetails['variant_name'];
            }

            if ($item->product_attribute_id) {
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $item->product_attribute_id])
                    ->contain([
                        'Attributes' => ['fields' => ['Attributes.name']],
                        'AttributeValues' => ['fields' => ['AttributeValues.value']]
                    ])
                    ->first();

                $item->product->attributes = $attributes ?: [];
            } else {
                $item->product->attributes = [];
            }

            $filteredItems[] = $item;
        }

        $response = [
            'status' => 'success',
            'order_products' => $filteredItems,
            'address' => $addressOptions
        ];

        return $this->response->withType('application/json')
            ->withStringBody(json_encode($response));
    }

    public function view($id = null)
    {
        $orderReturn = $this->OrderReturns->find()
            ->contain([
                'OrderItems' => [
                    'Products' => function ($q) {
                        return $q->select(['Products.id', 'Products.name', 'Products.sku']);
                    },
                    'ProductVariants' => function ($q) {
                        return $q->select(['ProductVariants.id', 'ProductVariants.variant_name', 'ProductVariants.sku']);
                    },
                    'ProductAttributes' => function ($q) {
                        return $q->select(['ProductAttributes.id', 'ProductAttributes.attribute_id', 'ProductAttributes.attribute_value_id']);
                    }
                ],
                'Orders' => [
                    'Customers' => ['Users']
                ],
                'Users' => function ($q) {
                    return $q->select(['Users.id', 'Users.first_name', 'Users.last_name']);
                },
                'VerifiedByUser' => function ($q) {
                    return $q->select(['VerifiedByUser.id', 'VerifiedByUser.first_name', 'VerifiedByUser.last_name']);
                },
                'OrderReturnCategories' => function ($q) {
                    return $q->select(['OrderReturnCategories.id', 'OrderReturnCategories.name']);
                }
            ])
            ->where(['OrderReturns.id' => $id])
            ->first();

        // Add return_to_name manually
        $returnTo = $orderReturn->return_to ?? null;
        $returnToId = $orderReturn->return_to_id ?? null;

        $returnToName = __('N/A');

        if(!empty($orderReturn->pickup_required) && strtolower($orderReturn->pickup_required) === 'yes') {
            if (strtolower($returnTo) === 'showroom' && $returnToId) {
                $showroom = $this->Showrooms->find()
                    ->select(['id', 'name'])
                    ->where(['id' => $returnToId])
                    ->first();

                if ($showroom) {
                    $returnToName = __('Showroom: ') . $showroom->name;
                }
            } elseif (strtolower($returnTo) === 'warehouse' && $returnToId) {
                $warehouse = $this->Warehouses->find()
                    ->select(['id', 'name'])
                    ->where(['id' => $returnToId])
                    ->first();

                if ($warehouse) {
                    $returnToName = __('Warehouse: ') . $warehouse->name;
                }
            }
        }

        $orderReturn->return_to_name = $returnToName;

        // Handle Product Attribute name/value if present
        if (!empty($orderReturn->order_item->product_attribute_id)) {

            $attribute = $this->ProductAttributes->find()
                ->where(['ProductAttributes.id' => $orderReturn->order_item->product_attribute_id])
                ->contain([
                    'Attributes' => ['fields' => ['Attributes.name']],
                    'AttributeValues' => ['fields' => ['AttributeValues.value']]
                ])
                ->first();

            if ($attribute) {
                $orderReturn->order_item->attributes = [
                    'attribute_name' => $attribute->attribute->name ?? '',
                    'attribute_value' => $attribute->attribute_value->value ?? ''
                ];
            }
        }

        // Handle Return Images
        if (!empty($orderReturn->return_product_image)) {
            $imageUrls = explode(',', $orderReturn->return_product_image);

            $imageUrls = array_map(function ($image) {
                return $this->Media->getCloudFrontURL(trim($image));
            }, $imageUrls);

            $orderReturn->return_product_image = implode(',', $imageUrls);
        }

        $warehouses = $this->Warehouses->find()
            ->where(['Warehouses.status' => 'A'])
            ->order(['Warehouses.name' => 'ASC'])
            ->toArray();

        $showrooms = $this->Showrooms->find()
                    ->where(['Showrooms.status' => 'A'])
                    ->order(['Showrooms.name' => 'ASC'])
                    ->toArray();

        // Fetch and format customer address (if available)
        $customerAddress = null;
        $customerAddressId = $orderReturn->customer_address_id ?? null;

        if ($customerAddressId) {

            $address = $this->CustomerAddresses->find()
                ->select([
                    'CustomerAddresses.id', 'CustomerAddresses.house_no', 'CustomerAddresses.address_line1',
                    'CustomerAddresses.address_line2', 'CustomerAddresses.zipcode',
                    'CustomerAddresses.city_id', 'CustomerAddresses.municipality_id'
                ])
                ->contain([
                    'Cities' => function ($q) {
                        return $q->select(['Cities.id', 'Cities.city_name']);
                    },
                    'Municipalities' => function ($q) {
                        return $q->select(['Municipalities.id', 'Municipalities.name']);
                    }
                ])
                ->where([
                    'CustomerAddresses.id' => $customerAddressId,
                    'CustomerAddresses.status' => 'A'
                ])
                ->first();

            if ($address) {
                $addressParts = [
                    h($address->house_no),
                    h($address->address_line1),
                    h($address->address_line2),
                    !empty($address->city) ? h($address->city->city_name) : '',
                    !empty($address->municipality) ? h($address->municipality->name) : '',
                    h($address->zipcode)
                ];
                $customerAddress = implode(', ', array_filter($addressParts));
            }
        }

        $this->set(compact('orderReturn', 'showrooms', 'warehouses', 'customerAddress'));
    }

    public function updatePickupDetails()
    {
        $this->request->allowMethod(['post', 'put']);
        $this->autoRender = false;

        $id = $this->request->getData('id');
        $orderReturn = $this->OrderReturns->get($id);

        $orderReturn->pickup_required = $this->request->getData('pickup_required');
        $orderReturn->pickup_charge = $this->request->getData('pickup_charge');

        if ($orderReturn->pickup_required === 'Yes') {
            $orderReturn->return_to = $this->request->getData('return_to');
            $orderReturn->return_to_id = ($orderReturn->return_to === 'Warehouse') 
                ? $this->request->getData('warehouse_id') 
                : $this->request->getData('showroom_id');
        } else {
            $orderReturn->return_to = null;
            $orderReturn->return_to_id = null;
        }

        if ($this->OrderReturns->save($orderReturn)) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => true,
                'message' => __('Pickup details updated successfully.')
            ]));
        }

        return $this->response->withType('application/json')->withStringBody(json_encode([
            'success' => false,
            'message' => __('Failed to update pickup details.')
        ]));
    }

    public function saveNote()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false; // No view rendered

        $response = ['success' => false, 'message' => ''];

        $data = $this->request->getData();

        if (empty($data['id'])) {
            $response['message'] = __('Invalid Order Return ID.');
            return $this->response->withType('application/json')->withStringBody(json_encode($response));
        }

        $orderReturn = $this->OrderReturns->get($data['id']);

        if (!$orderReturn) {
            $response['message'] = __('Order Return not found.');
            return $this->response->withType('application/json')->withStringBody(json_encode($response));
        }

        // Patch entity with new note
        $orderReturn = $this->OrderReturns->patchEntity($orderReturn, ['note' => $data['note']]);

        if ($this->OrderReturns->save($orderReturn)) {
            $response['success'] = true;
        } else {
            $response['message'] = __('Failed to save note. Please try again.');
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }

    public function rejectReturn()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;

        $response = ['success' => false, 'message' => ''];

        $id = $this->request->getData('id');
        if (empty($id)) {
            $response['message'] = __('Invalid Return ID.');
            return $this->response->withType('application/json')->withStringBody(json_encode($response));
        }

        try {
            $orderReturn = $this->OrderReturns->find()
                ->contain(['Users'])
                ->where(['OrderReturns.id' => $id])
                ->first();

            if (!$orderReturn) {
                $response['message'] = __('Return not found.');
                return $this->response->withType('application/json')->withStringBody(json_encode($response));
            }

            $user_detail = $this->Authentication->getIdentity();
            $orderReturn->status = __('Rejected');
            $orderReturn->verified_by = $user_detail->id;
            $orderReturn->verified_time = date('Y-m-d H:i:s');

            if (!$this->OrderReturns->save($orderReturn)) {
                $response['message'] = __('Failed to update return status.');
                return $this->response->withType('application/json')->withStringBody(json_encode($response));
            }

            if ($orderReturn->order_item_id) {
                $orderItem = $this->OrderItems->get($orderReturn->order_item_id);
                $orderItem->status = 'Return Rejected';

                if (!$this->OrderItems->save($orderItem)) {
                    $response['message'] = __('Failed to update order item status.');
                    return $this->response->withType('application/json')->withStringBody(json_encode($response));
                }

                // Add tracking history for order item
                $trackingData = [
                    'order_item_id' => $orderReturn->order_item_id,
                    'status' => 'Return Rejected',
                    'comment' => 'Return request rejected by admin'
                ];
                $this->OrderTrackingHistories->add_record($trackingData);
            }

            if ($orderReturn->order_id) {
                $order = $this->Orders->get($orderReturn->order_id);
                $order->status = 'Return Rejected';

                if (!$this->Orders->save($order)) {
                    $response['message'] = __('Failed to update order status.');
                    return $this->response->withType('application/json')->withStringBody(json_encode($response));
                }

                // Add tracking history for order
                $trackingData = [
                    'order_id' => $orderReturn->order_id,
                    'status' => 'Return Rejected',
                    'comment' => 'Return request rejected by admin'
                ];
                $this->OrderTrackingHistories->add_record($trackingData);
            }

            if (!empty($orderReturn->user->email)) {
                // Reload orderReturn with additional associations for email
                $orderReturn = $this->OrderReturns->find()
                    ->contain([
                        'OrderItems' => [
                            'Products',
                            'ProductVariants',
                            'ProductAttributes'
                        ],
                        'Orders' => ['Customers' => ['Users']],
                        'Users',
                        'VerifiedByUser',
                        'OrderReturnCategories'
                    ])
                    ->where(['OrderReturns.id' => $id])
                    ->first();

                $returnToName = 'N/A';
                if (!empty($orderReturn->return_to) && !empty($orderReturn->return_to_id)) {
                    if (strtolower($orderReturn->return_to) === 'showroom') {
                        $showroom = $this->Showrooms->find()
                            ->select(['id', 'name'])
                            ->where(['id' => $orderReturn->return_to_id])
                            ->first();
                        $returnToName = $showroom ? __('Showroom: ') . $showroom->name : $returnToName;
                    } elseif (strtolower($orderReturn->return_to) === 'warehouse') {
                        $warehouse = $this->Warehouses->find()
                            ->select(['id', 'name'])
                            ->where(['id' => $orderReturn->return_to_id])
                            ->first();
                        $returnToName = $warehouse ? __('Warehouse: ') . $warehouse->name : $returnToName;
                    }
                }
                $orderReturn->return_to_name = $returnToName;

                $attributeDetails = __('N/A');
                if (!empty($orderReturn->order_item->product_attribute_id)) {
                    $attribute = $this->ProductAttributes->find()
                        ->contain(['Attributes', 'AttributeValues'])
                        ->where(['ProductAttributes.id' => $orderReturn->order_item->product_attribute_id])
                        ->first();
                    if ($attribute) {
                        $attributeDetails = ($attribute->attribute->name ?? '') . ': ' . ($attribute->attribute_value->value ?? '');
                        $orderReturn->order_item->attributes = [
                            'attribute_name' => $attribute->attribute->name ?? '',
                            'attribute_value' => $attribute->attribute_value->value ?? ''
                        ];
                    }
                }

                $userName = trim($orderReturn->user->first_name . ' ' . $orderReturn->user->last_name);
                $greeting = __('Dear {0},', $userName);

                $emailData = [
                    'greeting' => $greeting,
                    'message' => __("Your return request for the following product has been rejected by the verification team."),
                    'request_id' => $orderReturn->id,
                    'requested_date' => $orderReturn->requested_at ? $orderReturn->requested_at->format('Y-m-d') : 'N/A',
                    'order_id' => $orderReturn->order_id,
                    'order_date' => $orderReturn->order->order_date ? $orderReturn->order->order_date->format('Y-m-d') : 'N/A',
                    'product_name' => $orderReturn->order_item->product->name ?? 'N/A',
                    'sku' => !empty($orderReturn->order_item->product_variant_id)
                        ? ($orderReturn->order_item->product_variant->sku ?? 'N/A')
                        : ($orderReturn->order_item->product->sku ?? 'N/A'),
                    'variant' => $orderReturn->order_item->product_variant->variant_name ?? 'N/A',
                    'attributes' => $attributeDetails,
                    'rejected_date' => date('d-m-Y'),
                    'rejected_by' => ($orderReturn->verified_by_user->first_name ?? '') . ' ' . ($orderReturn->verified_by_user->last_name ?? ''),
                    'status' => __('Rejected')
                ];

                $subject = __("Order Return Request Rejected - #{$orderReturn->id}");
                $toEmails = [$orderReturn->user->email];

                $this->Global->send_email(
                    $toEmails,
                    null,
                    $subject,
                    'return_request_rejected',
                    $emailData
                );
            }

            $response['success'] = true;
            $response['message'] = __('Return request rejected successfully.');
        } catch (\Exception $e) {
            $this->log("Error rejecting return: " . $e->getMessage(), 'error');
            $response['message'] = __('An unexpected error occurred. Please try again.');
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }

    public function approveReturn()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;

        $response = ['success' => false, 'message' => ''];

        $id = $this->request->getData('id');
        if (empty($id)) {
            $response['message'] = __('Invalid Return ID.');
            return $this->response->withType('application/json')->withStringBody(json_encode($response));
        }

        try {
            $orderReturn = $this->OrderReturns->find()
                ->contain(['Users'])
                ->where(['OrderReturns.id' => $id])
                ->first();

            if (!$orderReturn) {
                $response['message'] = __('Return not found.');
                return $this->response->withType('application/json')->withStringBody(json_encode($response));
            }

            $user_detail = $this->Authentication->getIdentity();
            $orderReturn->status = __('Approved');
            $orderReturn->verified_by = $user_detail->id;
            $orderReturn->verified_time = date('Y-m-d H:i:s');

            if (!$this->OrderReturns->save($orderReturn)) {
                $response['message'] = __('Failed to update return status.');
                return $this->response->withType('application/json')->withStringBody(json_encode($response));
            }

            if ($orderReturn->order_item_id) {
                $orderItem = $this->OrderItems->get($orderReturn->order_item_id);
                $orderItem->status = 'Return Approved';

                if (!$this->OrderItems->save($orderItem)) {
                    $response['message'] = __('Failed to update order item status.');
                    return $this->response->withType('application/json')->withStringBody(json_encode($response));
                }

                // Add tracking history for order item
                $trackingData = [
                    'order_item_id' => $orderReturn->order_item_id,
                    'status' => 'Return Approved',
                    'comment' => 'Return request approved by admin'
                ];
                $this->OrderTrackingHistories->add_record($trackingData);
            }

            if ($orderReturn->order_id) {
                $order = $this->Orders->get($orderReturn->order_id);
                $order->status = 'Return Approved';

                if (!$this->Orders->save($order)) {
                    $response['message'] = __('Failed to update order status.');
                    return $this->response->withType('application/json')->withStringBody(json_encode($response));
                }

                // Add tracking history for order
                $trackingData = [
                    'order_id' => $orderReturn->order_id,
                    'status' => 'Return Approved',
                    'comment' => 'Return request approved by admin'
                ];
                $this->OrderTrackingHistories->add_record($trackingData);
            }

            if (!empty($orderReturn->user->email)) {
                // Reload orderReturn with additional associations for email
                $orderReturn = $this->OrderReturns->find()
                    ->contain([
                        'OrderItems' => [
                            'Products',
                            'ProductVariants',
                            'ProductAttributes'
                        ],
                        'Orders' => ['Customers' => ['Users']],
                        'Users',
                        'VerifiedByUser',
                        'OrderReturnCategories'
                    ])
                    ->where(['OrderReturns.id' => $id])
                    ->first();

                $returnToName = 'N/A';
                if (!empty($orderReturn->return_to) && !empty($orderReturn->return_to_id)) {
                    if (strtolower($orderReturn->return_to) === 'showroom') {
                        $showroom = $this->Showrooms->find()
                            ->select(['id', 'name'])
                            ->where(['id' => $orderReturn->return_to_id])
                            ->first();
                        $returnToName = $showroom ? __('Showroom: ') . $showroom->name : $returnToName;
                    } elseif (strtolower($orderReturn->return_to) === 'warehouse') {
                        $warehouse = $this->Warehouses->find()
                            ->select(['id', 'name'])
                            ->where(['id' => $orderReturn->return_to_id])
                            ->first();
                        $returnToName = $warehouse ? __('Warehouse: ') . $warehouse->name : $returnToName;
                    }
                }
                $orderReturn->return_to_name = $returnToName;

                $attributeDetails = __('N/A');
                if (!empty($orderReturn->order_item->product_attribute_id)) {
                    $attribute = $this->ProductAttributes->find()
                        ->contain(['Attributes', 'AttributeValues'])
                        ->where(['ProductAttributes.id' => $orderReturn->order_item->product_attribute_id])
                        ->first();
                    if ($attribute) {
                        $attributeDetails = ($attribute->attribute->name ?? '') . ': ' . ($attribute->attribute_value->value ?? '');
                        $orderReturn->order_item->attributes = [
                            'attribute_name' => $attribute->attribute->name ?? '',
                            'attribute_value' => $attribute->attribute_value->value ?? ''
                        ];
                    }
                }

                // Get customer address
                $customerAddress = $this->CustomerAddresses->find()
                    ->where(['id' => $orderReturn->customer_address_id ?? 0])
                    ->first();

                $municipalityId = $customerAddress->municipality_id ?? null;
                $cityId = $customerAddress->city_id ?? null;

                // Get zone only if municipality_id is present
                $zoneId = null;
                if (!empty($municipalityId)) {
                    $zoneMunicipality = $this->ZoneMunicipalities->find()
                        ->where(['municipality_id' => $municipalityId, 'status' => 'A'])
                        ->first();

                    $zoneId = $zoneMunicipality->zone_id ?? null;
                }

                if (!empty($orderReturn->return_to)) {
                    // Create shipment
                    $shipment = $this->Shipments->newEmptyEntity();
                    $shipment->sender_type = $orderReturn->return_to;
                    $shipment->senderID = $orderReturn->return_to_id;
                    $shipment->shipment_status = 'Pending';
                    $shipment->delivery_status = 'Return Pickup';

                    if ($this->Shipments->save($shipment)) {
                        // Create shipment order
                        $shipmentOrder = $this->ShipmentOrders->newEmptyEntity();
                        $shipmentOrder->shipment_id = $shipment->id;
                        $shipmentOrder->order_id = $orderReturn->order_id;
                        $shipmentOrder->customer_id = $orderReturn->order->customer_id ?? null;
                        // $shipmentOrder->customer_address_id = $orderReturn->order->customer_address_id ?? null;
                        $shipmentOrder->customer_address_id = $orderReturn->customer_address_id ?? $orderReturn->order->customer_address_id ?? 0;
                        $shipmentOrder->city_id = $cityId;
                        $shipmentOrder->municipality_id = $municipalityId;
                        $shipmentOrder->zone_id = $zoneId;
                        $shipmentOrder->order_delivery_status = 'Return Pickup';

                        if ($this->ShipmentOrders->save($shipmentOrder)) {
                            $shipmentOrderItem = $this->ShipmentOrderItems->newEmptyEntity();
                            $shipmentOrderItem->shipment_order_id = $shipmentOrder->id;
                            $shipmentOrderItem->order_item_id = $orderReturn->order_item_id;
                            $shipmentOrderItem->quantity = $orderReturn->return_quantity;
                            $shipmentOrderItem->item_delivery_status = 'Return Pickup';
                            $this->ShipmentOrderItems->save($shipmentOrderItem);
                        }
                    }
                }

                $userName = trim($orderReturn->user->first_name . ' ' . $orderReturn->user->last_name);
                $greeting = __('Dear {0},', $userName);

                $emailData = [
                    'greeting' => $greeting,
                    'message' => __("Your return request for the following product has been approved by the verification team."),
                    'request_id' => $orderReturn->id,
                    'requested_date' => $orderReturn->requested_at ? $orderReturn->requested_at->format('Y-m-d') : 'N/A',
                    'order_id' => $orderReturn->order_id,
                    'order_date' => $orderReturn->order->order_date ? $orderReturn->order->order_date->format('Y-m-d') : 'N/A',
                    'product_name' => $orderReturn->order_item->product->name ?? 'N/A',
                    'sku' => !empty($orderReturn->order_item->product_variant_id)
                        ? ($orderReturn->order_item->product_variant->sku ?? 'N/A')
                        : ($orderReturn->order_item->product->sku ?? 'N/A'),
                    'variant' => $orderReturn->order_item->product_variant->variant_name ?? 'N/A',
                    'attributes' => $attributeDetails,
                    'rejected_date' => date('d-m-Y'),
                    'rejected_by' => ($orderReturn->verified_by_user->first_name ?? '') . ' ' . ($orderReturn->verified_by_user->last_name ?? ''),
                    'status' => __('Approved')
                ];

                $subject = __("Order Return Request Approved - #{$orderReturn->id}");

                // Send to Requested Person
                if (!empty($orderReturn->user->email)) {
                    $userName = trim($orderReturn->user->first_name . ' ' . $orderReturn->user->last_name);
                    $requesterEmailData = $emailData;
                    $requesterEmailData['greeting'] = __('Dear {0},', $userName);

                    $this->Global->send_email(
                        [$orderReturn->user->email],
                        null,
                        $subject,
                        'return_request_approved',
                        $requesterEmailData
                    );
                }

                // Send to Customer
                if (!empty($orderReturn->order->customer->user->email)) {
                    $customerName = trim($orderReturn->order->customer->user->first_name . ' ' . $orderReturn->order->customer->user->last_name);
                    $customerEmailData = $emailData;
                    $customerEmailData['greeting'] = __('Dear {0},', $customerName);

                    $this->Global->send_email(
                        [$orderReturn->order->customer->user->email],
                        null,
                        $subject,
                        'return_request_approved',
                        $customerEmailData
                    );
                }

                // -------------------------------
                // 🔹 Send Push Notification to Customer
                // -------------------------------
                if (!empty($orderReturn->order->customer->fcm_token)) {
                    $tokens = [$orderReturn->order->customer->fcm_token];
                    $title  = __('Return Request Approved');
                    $body   = __('Your return request for Order #{0} has been approved.', [$orderReturn->order->order_number ?? $orderReturn->order_id]);

                    $customData = [
                        'type'        => 'return_request',
                        'action'      => 'approved',
                        'request_id'  => $orderReturn->id,
                        'order_id'    => $orderReturn->order_id,
                        'order_number'=> $orderReturn->order->order_number ?? $orderReturn->order_id,
                        'status'      => 'Approved'
                    ];

                    $this->Global->sendNotification($tokens, $title, $body, $customData);
                }

            }

            $response['success'] = true;
            $response['message'] = __('Return request approved successfully.');
        } catch (\Exception $e) {
            $this->log("Error rejecting return: " . $e->getMessage(), 'error');
            $response['message'] = __('An unexpected error occurred. Please try again.');
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }

    public function processRefund()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;

        $response = ['success' => false, 'message' => ''];

        $id = $this->request->getData('id');
        if (empty($id)) {
            $response['message'] = __('Invalid Return ID.');
            return $this->response->withType('application/json')->withStringBody(json_encode($response));
        }

        try {
            $orderReturn = $this->OrderReturns->find()
                ->contain(['Users'])
                ->where(['OrderReturns.id' => $id])
                ->first();

            if (!$orderReturn) {
                $response['message'] = __('Return not found.');
                return $this->response->withType('application/json')->withStringBody(json_encode($response));
            }

            $user_detail = $this->Authentication->getIdentity();
            $orderReturn->status = __('Refund Pending');

            if (!$this->OrderReturns->save($orderReturn)) {
                $response['message'] = __('Failed to update return status.');
                return $this->response->withType('application/json')->withStringBody(json_encode($response));
            }

            $response['success'] = true;
            $response['message'] = __('Refund pending approved successfully.');
        } catch (\Exception $e) {
            $this->log("Error rejecting return: " . $e->getMessage(), 'error');
            $response['message'] = __('An unexpected error occurred. Please try again.');
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }


    /** CANCELLATION **/
    public function getPaidOrderItemsById($id = null)
    {
        // Fetch order + its transaction
        $order = $this->OrderItems->Orders->find()
            ->contain([
                'Transactions' => function ($q) {
                    return $q->select(['id', 'order_id', 'payment_method', 'payment_status']);
                }
            ])
            ->select(['id', 'customer_id', 'status'])
            ->where(['Orders.id' => $id])
            ->first();

        if (!$order || in_array($order->status, ['Returned', 'Deleted'])) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'status' => 'error',
                'message' => __('This order is either Cancelled, Returned, or Deleted and cannot be processed.')
            ]));
        }

        // Extract transaction (assuming 1 transaction per order)
        $transaction = $order->transactions[0] ?? null;
        $paymentMethod = $transaction->payment_method ?? null;
        $paymentStatus = $transaction->payment_status ?? null;

        // Validate payment status based on payment method
        if ($paymentMethod === 'Cash on Delivery') {
            // OK if Pending or Paid
            if (!in_array($paymentStatus, ['Pending', 'Paid'])) {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('COD payment must be Pending or Paid.')
                ]));
            }
        } else {
            // Other methods must be Paid
            if ($paymentStatus !== 'Paid') {
                return $this->response->withType('application/json')->withStringBody(json_encode([
                    'status' => 'error',
                    'message' => __('Only paid orders are allowed for this payment method.')
                ]));
            }
        }

        // Address options
        $addresses = $this->Orders->CustomerAddresses->find()
            ->where([
                'CustomerAddresses.customer_id' => $order->customer_id,
                'CustomerAddresses.status' => 'A'
            ])
            ->select([
                'CustomerAddresses.id', 'CustomerAddresses.house_no', 'CustomerAddresses.address_line1',
                'CustomerAddresses.address_line2', 'CustomerAddresses.zipcode',
                'CustomerAddresses.city_id', 'CustomerAddresses.municipality_id'
            ])
            ->contain([
                'Cities' => function ($q) {
                    return $q->select(['Cities.id', 'Cities.city_name']);
                },
                'Municipalities' => function ($q) {
                    return $q->select(['Municipalities.id', 'Municipalities.name']);
                }
            ])
            ->all();

        $addressOptions = [];
        foreach ($addresses as $address) {
            $addressParts = [
                h($address->house_no),
                h($address->address_line1),
                h($address->address_line2),
                !empty($address->city) ? h($address->city->city_name) : '',
                !empty($address->municipality) ? h($address->municipality->name) : '',
                h($address->zipcode)
            ];
            $addressOptions[$address->id] = implode(', ', array_filter($addressParts));
        }

        $excludedStatuses = ['Returned', 'Deleted']; //Removed Cancelled
        $order_items = $this->OrderItems->find()
            ->where([
                'OrderItems.order_id' => $id,
                'OrderItems.quantity >' => 0,
                'Orders.status NOT IN' => $excludedStatuses
            ])
            ->contain([
                'Orders' => ['fields' => ['id', 'status']],
                'Products' => function ($q) {
                    return $q->select(['Products.id', 'Products.name', 'Products.sku'])
                        ->contain([
                            'ProductVariants' => function ($q) {
                                return $q->select([
                                    'ProductVariants.product_id',
                                    'ProductVariants.id',
                                    'ProductVariants.sku',
                                    'ProductVariants.variant_name'
                                ]);
                            }
                        ]);
                }
            ])
            ->toArray();

        foreach ($order_items as $item) {
            $variantDetails = null;

            if ($item->product_variant_id) {
                $filtered_variants = array_filter($item->product->product_variants, function ($variant) use ($item) {
                    return $variant->id == $item->product_variant_id;
                });

                if (!empty($filtered_variants)) {
                    $variant = reset($filtered_variants);
                    $variantDetails = [
                        'variant_name' => $variant->variant_name,
                        'sku' => $variant->sku
                    ];
                }
            }

            $item->product->sku = $variantDetails['sku'] ?? $item->product->sku;

            if ($variantDetails) {
                $item->product->variant_name = $variantDetails['variant_name'];
            }

            if ($item->product_attribute_id) {
                $attributes = $this->ProductAttributes->find()
                    ->where(['ProductAttributes.id' => $item->product_attribute_id])
                    ->contain([
                        'Attributes' => ['fields' => ['Attributes.name']],
                        'AttributeValues' => ['fields' => ['AttributeValues.value']]
                    ])
                    ->first();

                $item->product->attributes = $attributes ?: [];
            } else {
                $item->product->attributes = [];
            }
        }

        return $this->response->withType('application/json')->withStringBody(json_encode([
            'status' => 'success',
            'order_products' => $order_items,
            'address' => $addressOptions
        ]));
    }

    public function addCancellation()
    {
        $returnRequest = $this->OrderReturns->newEmptyEntity();

        if ($this->request->is('post')) {

            $data = $this->request->getData();

            $orderId = $data['order_id'];
            $pickupRequired = __('No');
            $pickupCharge = __('No');
            $note = $data['note'] ?? null;
            $return_to = null;
            $return_to_id = null;
            $orderItemIds = $data['order_item_id'] ?? [];
            $prices = $data['price'] ?? [];
            $returnQuantities = $data['return_quantity'] ?? [];
            $returnReasons = $data['return_reason'] ?? [];
            $returnProductImages = []; 
            $customerAddressId = null;

            // Get logged-in user ID
            $user = $this->Authentication->getIdentity();
            $requestedBy = $user->id;

            for ($i = 0; $i < count($orderItemIds); $i++) {

                $returnImage = null;

                $returnAmount = (float)$returnQuantities[$i] * (float)$prices[$i];

                $orderReturnData = [
                    'order_id' => $orderId,
                    'order_item_id' => $orderItemIds[$i],
                    'order_return_category_id' => $returnReasons[$i],
                    'return_quantity' => $returnQuantities[$i],
                    'return_product_image' => $returnImage,
                    'request_type' => __('Cancellation'),
                    'status' => 'Pending',
                    'return_to' => $return_to,
                    'return_to_id' => $return_to_id,
                    'pickup_required' => $pickupRequired,
                    'pickup_charge' => $pickupCharge,
                    'note' => $note,
                    'requested_by' => $requestedBy,
                    'requested_at' => date('Y-m-d H:i:s'),
                    'return_amount' => $returnAmount,
                    'customer_address_id' => $customerAddressId,
                ];

                $orderReturn = $this->OrderReturns->newEntity($orderReturnData);

                if (!$this->OrderReturns->save($orderReturn)) {
                    $this->Flash->error(__('Failed to save cancellation entry for order item ID: ') . $orderItemIds[$i]);
                    return $this->redirect(['action' => 'add']);
                } else {
                    
                    $orderItem = $this->OrderItems->get($orderItemIds[$i]);
                    $orderItem->status = 'Pending Cancellation';
                    $this->OrderItems->save($orderItem);

                    $order = $this->Orders->get($orderId);
                    $order->status = 'Pending Cancellation';
                    $this->Orders->save($order);

                    // Reduce loyalty points for this cancelled order item
                    $orderItemSubtotal = (float)$orderItem->total_price;
                    $loyaltyResult = $this->Loyalty->reduceLoyaltyPointsForOrder(
                        $order->customer_id, 
                        $orderItemSubtotal, 
                        'Order item cancellation'
                    );
                    
                }
            }

            // Fetch one of the new OrderReturns
            $orderReturn = $this->OrderReturns->find()
                ->contain([
                    'OrderItems' => [
                        'Products',
                        'ProductVariants',
                        'ProductAttributes'
                    ],
                    'Orders' => ['Customers' => ['Users']],
                    'Users',
                    'OrderReturnCategories'
                ])
                ->where([
                    'OrderReturns.order_id' => $orderId,
                    'OrderReturns.request_type' => 'Cancellation',
                    'OrderReturns.status' => 'Pending'
                ])
                ->order(['OrderReturns.id' => 'DESC']) // last one submitted
                ->first();

            if (!empty($orderReturn->order->customer->user->email)) {
                $customerName = trim($orderReturn->order->customer->user->first_name . ' ' . $orderReturn->order->customer->user->last_name);
                $greeting = __('Dear {0},', $customerName);

                $attributeDetails = __('N/A');
                if (!empty($orderReturn->order_item->product_attribute_id)) {
                    $attribute = $this->ProductAttributes->find()
                        ->contain(['Attributes', 'AttributeValues'])
                        ->where(['ProductAttributes.id' => $orderReturn->order_item->product_attribute_id])
                        ->first();
                    if ($attribute) {
                        $attributeDetails = ($attribute->attribute->name ?? '') . ': ' . ($attribute->attribute_value->value ?? '');
                    }
                }

                $emailData = [
                    'greeting' => $greeting,
                    'message' => __("Your cancellation request for the following product has been submitted and is pending approval."),
                    'request_id' => $orderReturn->id,
                    'requested_date' => date('Y-m-d'),
                    'order_id' => $orderReturn->order_id,
                    'order_date' => $orderReturn->order->order_date ? $orderReturn->order->order_date->format('Y-m-d') : 'N/A',
                    'product_name' => $orderReturn->order_item->product->name ?? 'N/A',
                    'sku' => !empty($orderReturn->order_item->product_variant_id)
                        ? ($orderReturn->order_item->product_variant->sku ?? 'N/A')
                        : ($orderReturn->order_item->product->sku ?? 'N/A'),
                    'variant' => $orderReturn->order_item->product_variant->variant_name ?? 'N/A',
                    'attributes' => $attributeDetails,
                    'status' => __('Pending Cancellation'),
                    'requested_date' => date('Y-m-d')
                ];

                $subject = __("Order Cancel Request Submitted - #{$orderReturn->id}");

                $this->Global->send_email(
                    [$orderReturn->order->customer->user->email],
                    null,
                    $subject,
                    'cancel_request_submitted',
                    $emailData
                );
            }


            $this->Flash->success(__('Order cancellation(s) saved successfully.'));

            return $this->redirect(['controller' => 'ReturnsRefunds', 'action' => 'index']);
        }

        $order_ids = [];
        $order_return_categories_ids = [];

        try {
            $orders = $this->Orders->find()
                ->where([
                    'Orders.status' => 'Delivered'
                ])
                ->order(['Orders.created' => 'DESC'])
                ->all();

            if ($orders->isEmpty()) {
                // You can handle the case where no orders are found
                // For example: set a flag or message
                $noOrdersFound = true;
            } else {
                foreach ($orders as $order) {
                    if (!empty($order->id)) {
                        $order_ids[$order->id] = $order->id;
                    }
                }
            }


            $order_return_categories = $this->OrderReturnCategories->find()
                ->order(['OrderReturnCategories.id' => 'ASC'])
                ->all();

            if ($order_return_categories->isEmpty()) {
                // You can handle the case where no orders are found
                // For example: set a flag or message
                $noOrdersFound = true;
            } else {
                foreach ($order_return_categories as $order_return_category) {
                    if (!empty($order_return_category->id)) {
                        $order_return_categories_ids[$order_return_category->id] = $order_return_category->name;
                    }
                }
            }

        } catch (\Exception $e) {
            // Handle exception, for example by setting an error flag or message
            $errorMessage = __('Something went wrong while fetching delivered orders.');
        }

        $this->set(compact('order_ids', 'order_return_categories_ids'));

    }

    public function rejectCancellation()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;

        $response = ['success' => false, 'message' => ''];

        $id = $this->request->getData('id');
        if (empty($id)) {
            $response['message'] = __('Invalid Return ID.');
            return $this->response->withType('application/json')->withStringBody(json_encode($response));
        }

        try {
            $orderReturn = $this->OrderReturns->find()
                ->contain(['Users'])
                ->where(['OrderReturns.id' => $id])
                ->first();

            if (!$orderReturn) {
                $response['message'] = __('Return not found.');
                return $this->response->withType('application/json')->withStringBody(json_encode($response));
            }

            $user_detail = $this->Authentication->getIdentity();
            $orderReturn->status = __('Rejected');
            $orderReturn->verified_by = $user_detail->id;
            $orderReturn->verified_time = date('Y-m-d H:i:s');

            if (!$this->OrderReturns->save($orderReturn)) {
                $response['message'] = __('Failed to update return status.');
                return $this->response->withType('application/json')->withStringBody(json_encode($response));
            }

            if ($orderReturn->order_item_id) {
                $orderItem = $this->OrderItems->get($orderReturn->order_item_id);
                $orderItem->status = 'Cancellation Rejected';

                if (!$this->OrderItems->save($orderItem)) {
                    $response['message'] = __('Failed to update order item status.');
                    return $this->response->withType('application/json')->withStringBody(json_encode($response));
                }

                // Add tracking history for order item
                $trackingData = [
                    'order_item_id' => $orderReturn->order_item_id,
                    'status' => 'Cancellation Rejected',
                    'comment' => 'Cancellation request rejected by admin'
                ];
                $this->OrderTrackingHistories->add_record($trackingData);
            }

            if ($orderReturn->order_id) {
                $order = $this->Orders->get($orderReturn->order_id);
                $order->status = 'Cancellation Rejected';

                if (!$this->Orders->save($order)) {
                    $response['message'] = __('Failed to update order status.');
                    return $this->response->withType('application/json')->withStringBody(json_encode($response));
                }

                // Add tracking history for order
                $trackingData = [
                    'order_id' => $orderReturn->order_id,
                    'status' => 'Cancellation Rejected',
                    'comment' => 'Cancellation request rejected by admin'
                ];
                $this->OrderTrackingHistories->add_record($trackingData);
            }

            if (!empty($orderReturn->user->email)) {
                // Reload orderReturn with additional associations for email
                $orderReturn = $this->OrderReturns->find()
                    ->contain([
                        'OrderItems' => [
                            'Products',
                            'ProductVariants',
                            'ProductAttributes'
                        ],
                        'Orders' => ['Customers' => ['Users']],
                        'Users',
                        'VerifiedByUser',
                        'OrderReturnCategories'
                    ])
                    ->where(['OrderReturns.id' => $id])
                    ->first();

                $returnToName = 'N/A';
                if (!empty($orderReturn->return_to) && !empty($orderReturn->return_to_id)) {
                    if (strtolower($orderReturn->return_to) === 'showroom') {
                        $showroom = $this->Showrooms->find()
                            ->select(['id', 'name'])
                            ->where(['id' => $orderReturn->return_to_id])
                            ->first();
                        $returnToName = $showroom ? __('Showroom: ') . $showroom->name : $returnToName;
                    } elseif (strtolower($orderReturn->return_to) === 'warehouse') {
                        $warehouse = $this->Warehouses->find()
                            ->select(['id', 'name'])
                            ->where(['id' => $orderReturn->return_to_id])
                            ->first();
                        $returnToName = $warehouse ? __('Warehouse: ') . $warehouse->name : $returnToName;
                    }
                }
                $orderReturn->return_to_name = $returnToName;

                $attributeDetails = __('N/A');
                if (!empty($orderReturn->order_item->product_attribute_id)) {
                    $attribute = $this->ProductAttributes->find()
                        ->contain(['Attributes', 'AttributeValues'])
                        ->where(['ProductAttributes.id' => $orderReturn->order_item->product_attribute_id])
                        ->first();
                    if ($attribute) {
                        $attributeDetails = ($attribute->attribute->name ?? '') . ': ' . ($attribute->attribute_value->value ?? '');
                        $orderReturn->order_item->attributes = [
                            'attribute_name' => $attribute->attribute->name ?? '',
                            'attribute_value' => $attribute->attribute_value->value ?? ''
                        ];
                    }
                }

                $userName = trim($orderReturn->user->first_name . ' ' . $orderReturn->user->last_name);
                $greeting = __('Dear {0},', $userName);

                $emailData = [
                    'greeting' => $greeting,
                    'message' => __("Your cancel request for the following product has been rejected by the verification team."),
                    'request_id' => $orderReturn->id,
                    'requested_date' => $orderReturn->requested_at ? $orderReturn->requested_at->format('Y-m-d') : 'N/A',
                    'order_id' => $orderReturn->order_id,
                    'order_date' => $orderReturn->order->order_date ? $orderReturn->order->order_date->format('Y-m-d') : 'N/A',
                    'product_name' => $orderReturn->order_item->product->name ?? 'N/A',
                    'sku' => !empty($orderReturn->order_item->product_variant_id)
                        ? ($orderReturn->order_item->product_variant->sku ?? 'N/A')
                        : ($orderReturn->order_item->product->sku ?? 'N/A'),
                    'variant' => $orderReturn->order_item->product_variant->variant_name ?? 'N/A',
                    'attributes' => $attributeDetails,
                    'rejected_date' => date('d-m-Y'),
                    'rejected_by' => ($orderReturn->verified_by_user->first_name ?? '') . ' ' . ($orderReturn->verified_by_user->last_name ?? ''),
                    'status' => __('Rejected')
                ];

                $subject = __("Order Cancel Request Rejected - #{$orderReturn->id}");
                $toEmails = [$orderReturn->user->email, '<EMAIL>'];

                $this->Global->send_email(
                    $toEmails,
                    null,
                    $subject,
                    'cancel_request_rejected',
                    $emailData
                );

                // Send to Customer as well
                if (!empty($orderReturn->order->customer->user->email)) {
                    $customerName = trim($orderReturn->order->customer->user->first_name . ' ' . $orderReturn->order->customer->user->last_name);
                    $customerEmailData = $emailData;
                    $customerEmailData['greeting'] = __('Dear {0},', $customerName);

                    $this->Global->send_email(
                        [$orderReturn->order->customer->user->email],
                        null,
                        $subject,
                        'cancel_request_rejected',
                        $customerEmailData
                    );
                }


            }

            $response['success'] = true;
            $response['message'] = __('Cancel request rejected successfully.');
        } catch (\Exception $e) {
            $this->log("Error rejecting return: " . $e->getMessage(), 'error');
            $response['message'] = __('An unexpected error occurred. Please try again.');
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }

    // public function approveCancellation()
    // {
    //     $this->request->allowMethod(['post']);
    //     $this->autoRender = false;

    //     $response = ['success' => false, 'message' => ''];

    //     $id = $this->request->getData('id');
    //     if (empty($id)) {
    //         $response['message'] = __('Invalid Return ID.');
    //         return $this->response->withType('application/json')->withStringBody(json_encode($response));
    //     }

    //     try {
    //         $orderReturn = $this->OrderReturns->find()
    //             ->contain(['Users'])
    //             ->where(['OrderReturns.id' => $id])
    //             ->first();

    //         if (!$orderReturn) {
    //             $response['message'] = __('Return not found.');
    //             return $this->response->withType('application/json')->withStringBody(json_encode($response));
    //         }

    //         $user_detail = $this->Authentication->getIdentity();
    //         $orderReturn->status = __('Approved');
    //         $orderReturn->verified_by = $user_detail->id;
    //         $orderReturn->verified_time = date('Y-m-d H:i:s');

    //         if (!$this->OrderReturns->save($orderReturn)) {
    //             $response['message'] = __('Failed to update return status.');
    //             return $this->response->withType('application/json')->withStringBody(json_encode($response));
    //         }

    //         if ($orderReturn->order_item_id) {
    //             $orderItem = $this->OrderItems->get($orderReturn->order_item_id);
    //             $orderItem->status = 'Cancelled';

    //             if (!$this->OrderItems->save($orderItem)) {
    //                 $response['message'] = __('Failed to update order item status.');
    //                 return $this->response->withType('application/json')->withStringBody(json_encode($response));
    //             }
    //         }

    //         if ($orderReturn->order_id) {
    //             $order = $this->Orders->get($orderReturn->order_id);
    //             $order->status = 'Cancelled';

    //             if (!$this->Orders->save($order)) {
    //                 $response['message'] = __('Failed to update order status.');
    //                 return $this->response->withType('application/json')->withStringBody(json_encode($response));
    //             }
    //         }

    //         if (!empty($orderReturn->user->email)) {
    //             // Reload orderReturn with additional associations for email
    //             $orderReturn = $this->OrderReturns->find()
    //                 ->contain([
    //                     'OrderItems' => [
    //                         'Products',
    //                         'ProductVariants',
    //                         'ProductAttributes'
    //                     ],
    //                     'Orders' => ['Customers' => ['Users']],
    //                     'Users',
    //                     'VerifiedByUser',
    //                     'OrderReturnCategories'
    //                 ])
    //                 ->where(['OrderReturns.id' => $id])
    //                 ->first();

    //             $returnToName = __('N/A');
    //             if (!empty($orderReturn->return_to) && !empty($orderReturn->return_to_id)) {
    //                 if (strtolower($orderReturn->return_to) === 'showroom') {
    //                     $showroom = $this->Showrooms->find()
    //                         ->select(['id', 'name'])
    //                         ->where(['id' => $orderReturn->return_to_id])
    //                         ->first();
    //                     $returnToName = $showroom ? __('Showroom: ') . $showroom->name : $returnToName;
    //                 } elseif (strtolower($orderReturn->return_to) === 'warehouse') {
    //                     $warehouse = $this->Warehouses->find()
    //                         ->select(['id', 'name'])
    //                         ->where(['id' => $orderReturn->return_to_id])
    //                         ->first();
    //                     $returnToName = $warehouse ? __('Warehouse: ') . $warehouse->name : $returnToName;
    //                 }
    //             }
    //             $orderReturn->return_to_name = $returnToName;

    //             $attributeDetails = __('N/A');
    //             if (!empty($orderReturn->order_item->product_attribute_id)) {
    //                 $attribute = $this->ProductAttributes->find()
    //                     ->contain(['Attributes', 'AttributeValues'])
    //                     ->where(['ProductAttributes.id' => $orderReturn->order_item->product_attribute_id])
    //                     ->first();
    //                 if ($attribute) {
    //                     $attributeDetails = ($attribute->attribute->name ?? '') . ': ' . ($attribute->attribute_value->value ?? '');
    //                     $orderReturn->order_item->attributes = [
    //                         'attribute_name' => $attribute->attribute->name ?? '',
    //                         'attribute_value' => $attribute->attribute_value->value ?? ''
    //                     ];
    //                 }
    //             }

    //             $userName = trim($orderReturn->user->first_name . ' ' . $orderReturn->user->last_name);
    //             $greeting = __('Dear {0},', $userName);

    //             $emailData = [
    //                 'greeting' => $greeting,
    //                 'message' => __("Your cancel request for the following product has been approved by the verification team."),
    //                 'request_id' => $orderReturn->id,
    //                 'requested_date' => $orderReturn->requested_at ? $orderReturn->requested_at->format('Y-m-d') : 'N/A',
    //                 'order_id' => $orderReturn->order_id,
    //                 'order_date' => $orderReturn->order->order_date ? $orderReturn->order->order_date->format('Y-m-d') : 'N/A',
    //                 'product_name' => $orderReturn->order_item->product->name ?? 'N/A',
    //                 'sku' => !empty($orderReturn->order_item->product_variant_id)
    //                     ? ($orderReturn->order_item->product_variant->sku ?? 'N/A')
    //                     : ($orderReturn->order_item->product->sku ?? 'N/A'),
    //                 'variant' => $orderReturn->order_item->product_variant->variant_name ?? 'N/A',
    //                 'attributes' => $attributeDetails,
    //                 'rejected_date' => date('d-m-Y'),
    //                 'rejected_by' => ($orderReturn->verified_by_user->first_name ?? '') . ' ' . ($orderReturn->verified_by_user->last_name ?? ''),
    //                 'status' => __('Approved')
    //             ];

    //             $subject = __("Order Cancel Request Approved - #{$orderReturn->id}");

    //             // Send to Requested Person
    //             if (!empty($orderReturn->user->email)) {
    //                 $userName = trim($orderReturn->user->first_name . ' ' . $orderReturn->user->last_name);
    //                 $requesterEmailData = $emailData;
    //                 $requesterEmailData['greeting'] = __('Dear {0},', $userName);

    //                 $this->Global->send_email(
    //                     [$orderReturn->user->email],
    //                     null,
    //                     $subject,
    //                     'return_request_approved',
    //                     $requesterEmailData
    //                 );
    //             }

    //             // Send to Customer
    //             if (!empty($orderReturn->order->customer->user->email)) {
    //                 $customerName = trim($orderReturn->order->customer->user->first_name . ' ' . $orderReturn->order->customer->user->last_name);
    //                 $customerEmailData = $emailData;
    //                 $customerEmailData['greeting'] = __('Dear {0},', $customerName);

    //                 $this->Global->send_email(
    //                     [$orderReturn->order->customer->user->email],
    //                     null,
    //                     $subject,
    //                     'cancel_request_approved',
    //                     $customerEmailData
    //                 );
    //             }


    //         }

    //         $response['success'] = true;
    //         $response['message'] = __('Cancel request approved successfully.');
    //     } catch (\Exception $e) {
    //         $this->log("Error rejecting return: " . $e->getMessage(), 'error');
    //         $response['message'] = __('An unexpected error occurred. Please try again.');
    //     }

    //     return $this->response->withType('application/json')->withStringBody(json_encode($response));
    // }

    public function approveCancellation()
    {
        $this->request->allowMethod(['post']);
        $this->autoRender = false;

        $response = ['success' => false, 'message' => ''];

        $id = $this->request->getData('id');
        if (empty($id)) {
            $response['message'] = __('Invalid Return ID.');
            return $this->response->withType('application/json')->withStringBody(json_encode($response));
        }

        try {
            $orderReturn = $this->OrderReturns->find()
                ->contain(['Users', 'OrderItems', 'Orders'])
                ->where(['OrderReturns.id' => $id])
                ->first();

            if (!$orderReturn) {
                $response['message'] = __('Return not found.');
                return $this->response->withType('application/json')->withStringBody(json_encode($response));
            }

            $user_detail = $this->Authentication->getIdentity();
            $orderReturn->status = __('Approved');
            $orderReturn->verified_by = $user_detail->id;
            $orderReturn->verified_time = date('Y-m-d H:i:s');

            if (!$this->OrderReturns->save($orderReturn)) {
                $response['message'] = __('Failed to update return status.');
                return $this->response->withType('application/json')->withStringBody(json_encode($response));
            }

            if ($orderReturn->order_item_id) {
                $orderItem = $this->OrderItems->get($orderReturn->order_item_id);
                $orderItem->status = 'Cancelled';

                if (!$this->OrderItems->save($orderItem)) {
                    $response['message'] = __('Failed to update order item status.');
                    return $this->response->withType('application/json')->withStringBody(json_encode($response));
                }

                // Add tracking history for order item
                $trackingData = [
                    'order_item_id' => $orderReturn->order_item_id,
                    'status' => 'Cancelled',
                    'comment' => 'Cancellation request approved by admin'
                ];
                $this->OrderTrackingHistories->add_record($trackingData);

                if (!empty($orderReturn->order) 
                    && $orderReturn->order->delivery_mode === 'pickup' 
                    && !empty($orderReturn->order->showroom_id)) 
                {
                    $conditions = [
                        'showroom_id' => $orderReturn->order->showroom_id,
                        'product_id' => $orderItem->product_id
                    ];

                    if (!empty($orderItem->product_variant_id)) {
                        $conditions['product_variant_id'] = $orderItem->product_variant_id;
                    } else {
                        $conditions['product_variant_id IS'] = null;
                    }

                    if (!empty($orderItem->product_attribute_id)) {
                        $conditions['product_attribute_id'] = $orderItem->product_attribute_id;
                    } else {
                        $conditions['product_attribute_id IS'] = null;
                    }

                    $stock = $this->ProductStocks->find()
                        ->where($conditions)
                        ->first();

                    if ($stock) {
                        // Deduct only the returned quantity
                        $deductQty = (int)$orderReturn->return_quantity;

                        $stock->reserved_stock = max(0, $stock->reserved_stock - $deductQty);
                        $this->ProductStocks->save($stock);
                    }
                }

            }

            if ($orderReturn->order_id) {
                $order = $this->Orders->get($orderReturn->order_id);
                $order->status = 'Cancelled';

                if (!$this->Orders->save($order)) {
                    $response['message'] = __('Failed to update order status.');
                    return $this->response->withType('application/json')->withStringBody(json_encode($response));
                }

                // Add tracking history for order
                $trackingData = [
                    'order_id' => $orderReturn->order_id,
                    'status' => 'Cancelled',
                    'comment' => 'Cancellation request approved by admin'
                ];
                $this->OrderTrackingHistories->add_record($trackingData);
            }

            // … (your existing email notification code remains unchanged) …

            $response['success'] = true;
            $response['message'] = __('Cancel request approved successfully.');
        } catch (\Exception $e) {
            $this->log("Error approving cancellation: " . $e->getMessage(), 'error');
            $response['message'] = __('An unexpected error occurred. Please try again.');
        }

        return $this->response->withType('application/json')->withStringBody(json_encode($response));
    }


}
