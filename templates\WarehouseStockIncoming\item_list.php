<?php
/**
 * @var \App\View\AppView $this
 * @var iterable<\App\Model\Entity\User> $users
 */
?>
<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('bundles/datatables/datatables.min.css') ?>">
<link rel="stylesheet"
    href="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') ?>">
<style>
    .barcode-table > img {
        display: block;
        margin: 0 auto;
        width: 35%;
        height: 35%;
    }
</style>
<?php $this->end(); ?>
<div class="section-header">
    <ul class="breadcrumb breadcrumb-style ">
        <li class="breadcrumb-item">
            <a href="<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'index']) ?>">
                <h4 class="page-title m-b-0"><?= __("Dashboard") ?></h4>
            </a>
        </li>
        <li class="breadcrumb-item"><a href="#" onclick="history.back()"><?= __('Incoming Stock to Warehouse') ?></a>
        </li>
        <!-- <li class="breadcrumb-item"><a
                href="<?= $this->Url->build(['controller' => 'WarehouseStockIncoming', 'action' => 'addIncoming']) ?>"><?= __('Add') ?></a>
        </li> -->
        <li class="breadcrumb-item active"><a href="#"><?= __("Product Items") ?></a></li>
    </ul>
</div>  
<div class="section-body1">
    <div class="container-fluid">
        <?= $this->Flash->render() ?>
    </div>
</div>
<div class="section-body" id="list">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h4><?= __("Product Items") ?></h4>
                

                <div class="card-header-form">
                    <div class="input-group">
                        
                        <button type="button" class="btn btn-success mb-3" onclick="history.back()">Back</button>

                        <button class="btn btn-success mb-3" onclick="printBarcodes()"><?= __('Print QR Codes') ?></button>

                    </div>
                </div>
            </div>
            
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped"
                        id="table-1" style="width: 700px;">
                        <thead>
                            <tr>
                                
                                <th class="barcode-table">ID</th>
                                <th class="barcode-table">Product QR Codes</th>
                                
                            </tr>
                        </thead>
                        <tbody id="barcodeArea" >
                            <?php
                            
                            $i = 1;
                            foreach ($stockitems as $item):
                                $product = $this->WebsiteFunction->getProductById($item->product_id);
                                        if(!is_null($item->varriant_id) && $item->varriant_id != 0):
                                        $variant = $this->WebsiteFunction->getVariantById($item->varriant_id);
                                    else:
                                        $variant=null;
                                    endif;
                                    
                                ?>
                                <tr style="text-align: center;">
                                    <td></td>
                                    <td class="barcode-table" style="padding: 20px 0;">
                                        <img src="<?= \Cake\Routing\Router::url('/', true) . $item->barcode ?>"  />
                                        <div class="barcode_unique_id">
                                            <?= h($item->unique_id) ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php $i++;
                            endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
</div>
<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/datatables/datatables.min.js') ?>"></script>
<script
    src="<?= $this->Url->webroot('bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/delete.js'); ?>"></script>
<script src="<?= $this->Url->webroot('js/filter.js') ?>"></script>
<script>
    var paginationCount = <?= json_encode($paginationCount) ?>;
    var table = $("#table-1").DataTable({
        columnDefs: [
            { orderable: false, targets: -1 }, // Make the last column non-sortable
            { visible: false, targets: 0 }
        ],
        dom: 'rtip',
        pageLength: paginationCount,
        drawCallback: function() {
            var api = this.api();
            api.column(0, {search: 'applied', order: 'applied'}).nodes().each(function(cell, i) {
                cell.innerHTML = i + 1;
            });
        }
    });

    // table.column(5).search('Active', true, false, false).draw();

    $('#customSearchBox').on('keyup', function () {
        table.search(this.value).draw();
    });

    function resetFilters() {
        $('#customSearchBox').val('');
        $('#filterStatus').val('');
        table.search('').columns().search('').draw();
        table.column(5).search('Active', true, false, false).draw();
        table.draw();
    }

    // $('#filter').on('click', function (event) {
    //     event.preventDefault();
    //     var status = $("#filterStatus").val();
    //     var statusMap = <?= json_encode($status) ?>;
    //     var statusVal = statusMap[status] !== undefined ? statusMap[status] : '';
        
    //     if (statusVal == '') {
    //         table.column(5).search('Active', true, false, false).draw();
    //     } else {
    //         table.column(5).search(statusVal, true, false, false).draw();
    //     }
    //     table.draw();
    // });

    //Barcode Print function
    function printBarcodes() {
        const printContents = document.getElementById('barcodeArea').innerHTML;
        const originalContents = document.body.innerHTML;

        document.body.innerHTML = printContents;
        window.print();
        document.body.innerHTML = originalContents;
        location.reload(); // reload to restore functionality
    }
    
</script>
<?php $this->end(); ?>